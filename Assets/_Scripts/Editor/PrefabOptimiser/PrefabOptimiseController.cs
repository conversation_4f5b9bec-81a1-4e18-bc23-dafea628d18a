using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using Unity.EditorCoroutines.Editor;
using UnityEditor;
using UnityEngine;

public static class PrefabOptimiseController
{
    #region Constants

    public const string c_DocLink = "https://22cans.monday.com/docs/9079875105";
    public const string c_ConfigStateKey = "OptimiserConfig";
    public const string c_AutoStateKey = "AutoOptimise";
    public const string c_ConfigFolder = "Assets/PrefabOptimiser";
    public const string c_ConfigMasterName = "kitbash_default.json";
    public const string c_DefaultInputFolder = "Assets/_Prefabs/_Blocks";
    public const string c_DefaultOutputFolder = "Assets/Resources/_Prefabs/_Blocks";
    public static readonly HashSet<string> c_BlacklistedExtensions = new() { ".cs", ".info", ".shader", "" };
    
    #endregion Constants
    
    
    #region Classes
    
    [Serializable] public class Configuration
    {
        public string name;

        public string directoryFrom;
        public string directoryTo;
        public bool superFolder;
        public string atlasName = "Atlas";
        public bool enabled = true;
        
        [NonSerialized] public List<SceneAsset> targetScenes = new();
        [NonSerialized] public List<GameObject> targetPrefabs = new();
        public List<string> targetGUIDs = new();

        public Configuration(string _folderIn, string _folderOut, bool _superFolder)
        {
            name = Path.GetFileName(_folderIn);
            directoryFrom = $"Assets/{Path.GetRelativePath(Application.dataPath, _folderIn).Replace('\\', '/')}";
            directoryTo = $"Assets/{Path.GetRelativePath(Application.dataPath, _folderOut).Replace('\\', '/')}";

            superFolder = _superFolder;
        }

        public void OnBeforeSerialize()
        {
            targetGUIDs = new();
            if (targetScenes != null)
            {
                foreach (var scene in targetScenes)
                    targetGUIDs.Add(AssetDatabase.AssetPathToGUID(AssetDatabase.GetAssetPath(scene)));
            }
            if (targetPrefabs != null)
            {
                foreach (var prefab in targetPrefabs)
                    targetGUIDs.Add(AssetDatabase.AssetPathToGUID(AssetDatabase.GetAssetPath(prefab)));
            }
        }

        public void OnAfterDeserialize()
        {
            targetScenes = new();
            targetPrefabs = new();
            if (targetGUIDs == null)
                return;
            foreach (var guid in targetGUIDs)
            {
                var scene = AssetDatabase.LoadAssetAtPath<SceneAsset>(AssetDatabase.GUIDToAssetPath(guid));
                if (scene != null)
                    targetScenes.Add(scene);
                else
                {
                    var prefab = AssetDatabase.LoadAssetAtPath<GameObject>(AssetDatabase.GUIDToAssetPath(guid));
                    if (prefab != null)
                        targetPrefabs.Add(prefab);
                }
            }
        }
    }

    [Serializable] private class ConfigurationListWrapper
    {
        public List<Configuration> configurations = new();
    }
    
    #endregion Classes
    
    
    #region Fields
    
    public static bool forceUpdate;
    private static bool isRunning;
    private static List<Configuration> configurationCache;
    
    private static EditorCoroutine currentOperation;
    private static EditorCoroutine popupCoroutine;

    #endregion Fields
    
    
    #region Getters/Setters
    
    public static bool IsRunning => isRunning;
    
    #region Session State
    
    public static bool autoOptimise
    {
        get => false;//SessionState.GetBool(c_AutoStateKey, true);
        set => SessionState.SetBool(c_AutoStateKey, value);
    }

    private static bool isDirty
    {
        get => SessionState.GetBool("OptimiserDirty", false);
        set => SessionState.SetBool("OptimiserDirty", value);
    }
    
    #endregion Session State
    
    #endregion Getters/Setters
    
    
    #region Public Methods
    
    public static void SetConfigs(List<Configuration> _configs)
    {            
        configurationCache = _configs;
        foreach (var config in configurationCache)
            config.OnBeforeSerialize();
        var wrapper = new ConfigurationListWrapper{configurations = configurationCache};
        string json = JsonUtility.ToJson(wrapper, true);
        SessionState.SetString(c_ConfigStateKey, json);
    }
    
    public static List<Configuration> GetConfigs()
    {
        if (configurationCache is not { Count: > 0 })
        {
            var json = SessionState.GetString(c_ConfigStateKey, null);
            if (string.IsNullOrEmpty(json))
            {
                json = File.ReadAllText($"{c_ConfigFolder}/{c_ConfigMasterName}");
                SessionState.SetString(c_ConfigStateKey, json);
            }
            configurationCache = JsonUtility.FromJson<ConfigurationListWrapper>(json).configurations;
            foreach (var config in configurationCache)
                config.OnAfterDeserialize();
        }
        return configurationCache;
    }
    
    public static bool IsRelevantAsset(string assetPath)
    {
        return !c_BlacklistedExtensions.Contains(Path.GetExtension(assetPath));
    }
    
    public static void StartTryRunAll(bool _auto = false)
    {
        if (popupCoroutine != null)
            return;
        popupCoroutine = EditorCoroutineUtility.StartCoroutineOwnerless(TryRunAll(!_auto));
    }
    
    public static bool CanRun(out string _whyNot)
    {
        _whyNot = null;
        
        if (Application.isPlaying)
        {
            _whyNot = "Cannot run while in play mode.";
            return false;
        }
        if (isRunning)
        {
            _whyNot = "Already running.";
            return false;
        }
        bool configEnabled = false;
        foreach (var config in GetConfigs())
        {
            if (!config.enabled) 
                continue;
            configEnabled = true;
            if (!ValidateConfiguration(config))
            {
                _whyNot = $"Configuration {config.name} not valid.";
                return false;
            }
        }
        if (!configEnabled)
            _whyNot = "No configurations enabled.";
        return configEnabled;
    }
    
    public static List<Configuration> GetSplitConfigs() => CreateSplitConfigurations(GetConfigs());

    public static Configuration GetNthConfig(ref int _index, out bool _hasConfigs)
    {
        var configs = GetConfigs();
        _hasConfigs = configs is { Count: > 0 };
        if (!_hasConfigs) return null;
        if (_index < 0 || _index >= configs.Count)
            _index = 0;
        return configs[_index];
    }
    
    public static void SetNthConfig(Configuration _config, int _index)
    {
        var configs = GetConfigs();
        configs[_index] = _config;
        SetConfigs(configs);
    }
    
    public static int AddConfig(string _folderIn, string _folderOut, bool _superFolder)
    {
        var config = new Configuration(_folderIn, _folderOut, _superFolder);
        var configs = GetConfigs();
        configs.Add(config);
        SetConfigs(configs);
        return configs.Count - 1;
    }
    
    public static void ReadInConfig(string _path)
    {
        string json = File.ReadAllText(_path);
        SessionState.SetString(c_ConfigStateKey, json);
        configurationCache = null; // Force reload
    }
    
    public static void WriteOutConfig(string _path)
    {
        SetConfigs(configurationCache); //Refresh cache
        string json = SessionState.GetString(c_ConfigStateKey, "");
        File.WriteAllText(_path, json);
    }
    
    public static void ClearConfiguration()
    {
        SetConfigs(new());
    }
    
    public static void RunAllConfigurations()
    {
        if (!CanRun(out var reason))
        {
            EditorUtility.DisplayDialog("Cannot Run Optimiser",
                $"Optimiser Error: {reason}", "OK");
            return;
        }

        currentOperation = EditorCoroutineUtility.StartCoroutineOwnerless(RunAll(GetConfigs()));
    }
    
    public static void ForceStop()
    {
        if (currentOperation != null)
        {
            EditorCoroutineUtility.StopCoroutine(currentOperation);
            currentOperation = null;
        }

        isRunning = false;
    }
    
    #endregion Public Methods
    
    
    #region Private Methods
    
    private static void CheckDirty(bool _forceFalse = false)
    {
        bool isNowDirty = !_forceFalse;
        if (isNowDirty)
            isNowDirty &= CollectDirty(GetSplitConfigs()).Count > 0;
        if (isNowDirty == isDirty) 
            return;
        
        isDirty = isNowDirty;
        if (isDirty)
            UnityToolbarExtender.ToolbarExtender.LeftToolbarGUI.Add(ToolbarWarningButton);
        else
            UnityToolbarExtender.ToolbarExtender.LeftToolbarGUI.Remove(ToolbarWarningButton);
    }
    
    private static IEnumerator TryRunAll(bool force)
    {
        while (Application.isPlaying)
            yield return null;

        if (!CanRun(out var reason))
        {
            EditorUtility.DisplayDialog("Cannot Run Optimiser",
                $"Optimiser Error: {reason}", "OK");
            popupCoroutine = null;
            yield break;
        }

        if (force)
        {
            RunAllConfigurations();
            popupCoroutine = null;
            yield break;
        }

        if (!autoOptimise)
        {
            CheckDirty();
            popupCoroutine = null;
            yield break;
        }

        var choice1 = 2;
        while (choice1 == 2)
        {
            choice1 = EditorUtility.DisplayDialogComplex("Prefab Optimiser",
                "I've detected potential changes. Would you like to run the optimiser now?",
                "Yes please", "No thanks", "Open User Guide");
            switch (choice1)
            {
                case 0:
                    RunAllConfigurations();
                    break;
                case 1:
                    var choice2 = EditorUtility.DisplayDialogComplex("Prefab Optimiser",
                        "Would you like to be reminded next time I detect changes?",
                        "Yes!", "Back", "NO, FUCK OFF!");
                    if (choice2 == 1)
                    {
                        choice1 = 2;
                        break;
                    }
                    if (choice2 == 2)
                        autoOptimise = false;
                    CheckDirty();
                    break;
                case 2:
                    Application.OpenURL(c_DocLink);
                    GUIUtility.systemCopyBuffer = c_DocLink;
                    break;
            }
        }
        popupCoroutine = null;
    }

    private static void ToolbarWarningButton() => OptimisePrefabWindow.ToolbarWarningButton();

    [InitializeOnLoadMethod]
    private static void RegisterToolbarWarningButton()  
    {
        if (isDirty)
            UnityToolbarExtender.ToolbarExtender.LeftToolbarGUI.Add(ToolbarWarningButton);
    }

    private static IEnumerator RunAll(List<Configuration> _config)
    {
        isRunning = true;
        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh(ImportAssetOptions.ForceSynchronousImport);

        var validInputFolders = CollectValidInputFolders(_config);
        PruneOutputFolders(_config, validInputFolders);

        var splitConfigs = CreateSplitConfigurations(_config);
        
        var dirtyConfigs = CollectDirty(splitConfigs);
        if (dirtyConfigs.Count == 0)
        {
            CheckDirty(true);
            EditorUtility.DisplayDialog("Prefab Optimiser Cancelled", "No changes detected, nothing to optimise.", "OK");
            isRunning = false;
            yield break;
        }
        
        var task = HierarchicalSelector.GetSelection(dirtyConfigs.Select(x => x.directoryFrom).ToList());
        while (!task.IsCompleted)
            yield return null;
        
        if (task.Result == null || task.Result.Count == 0)
        {
            EditorUtility.DisplayDialog("Prefab Optimiser Cancelled", "No folders selected for optimisation.", "OK");
            CheckDirty();
            isRunning = false;
            yield break;
        }
        var unfilteredLength = dirtyConfigs.Count;
        dirtyConfigs = dirtyConfigs.Where(x => task.Result.Contains(x.directoryFrom)).ToList();
        CheckDirty(unfilteredLength == dirtyConfigs.Count);
        
        OptimiserReport.Create(dirtyConfigs, "Assets/PrefabOptimiser/Report.asset");
        OptimiserReport.StartSection("Everything", "The entire process!");
            
        yield return ProcessAllConfigurations(dirtyConfigs);
        
        OptimiserReport.EndSection("Everything");
        OptimiserReport.FinishReport();
        
        isRunning = false;
        ShowFinishedPopup(dirtyConfigs);
    }

    private static void ShowFinishedPopup(List<Configuration> _config)
    {
        var finishedNote = new StringBuilder();
        finishedNote.Append("Please ensure you push all of your authored changes, " +
                            "all of your optimiser.info changes, and every generated asset folder:\n\n");
        foreach (var config in _config)
            finishedNote.Append($"{config.directoryTo},\n");
        finishedNote.Remove(finishedNote.Length - 2, 2);
        
        EditorUtility.DisplayDialog("Prefab Optimiser Completed", finishedNote.ToString(), "OK");
    }

    private static HashSet<string> CollectValidInputFolders(List<Configuration> configs)
    {
        var validInputFolders = new HashSet<string>();

        foreach (var config in configs)
        {
            if (!config.enabled || !Directory.Exists(config.directoryFrom))
                continue;

            if (config.superFolder)
            {
                var subFolders = Directory.GetDirectories(config.directoryFrom);
                foreach (var subFolder in subFolders)
                {
                    validInputFolders.Add(NormalizePath(subFolder));
                }
            }
            else
            {
                validInputFolders.Add(NormalizePath(config.directoryFrom));
            }
        }

        return validInputFolders;
    }

    private static void PruneOutputFolders(List<Configuration> configs, HashSet<string> inputFolders)
    {
        foreach (var config in configs)
        {
            if (!config.enabled || !Directory.Exists(config.directoryTo))
                continue;

            if (config.superFolder)
                CleanupSuperFolderOutputs(config, inputFolders);
            else
                CleanupSingleFolderOutput(config, inputFolders);
        }
    }

    private static void CleanupSuperFolderOutputs(Configuration config, HashSet<string> inputFolders)
    {
        var outputSubFolders = Directory.GetDirectories(config.directoryTo);
        foreach (var outputSubFolder in outputSubFolders)
        {
            string normalizedOutputPath = NormalizePath(outputSubFolder);
            string inputFolderName = Path.GetFileName(normalizedOutputPath);
            string expectedInputPath = NormalizePath(Path.Combine(config.directoryFrom, inputFolderName));

            if (!inputFolders.Contains(expectedInputPath))
                Directory.Delete(normalizedOutputPath, true);
        }
    }

    private static void CleanupSingleFolderOutput(Configuration config, HashSet<string> inputFolders)
    {
        if (!inputFolders.Contains(NormalizePath(config.directoryFrom)))
            Directory.Delete(config.directoryTo, true);
    }
    
    private static string NormalizePath(string path) => path.Replace('\\', '/');

    private static List<Configuration> CollectDirty(List<Configuration> configs)
    {
        if (forceUpdate)
            return configs;
        
        var outList = new List<Configuration>();
        foreach (var config in configs)
        {
            if (PrefabOptimiser.ShouldRun(config))
                outList.Add(config);
        }

        return outList;
    }
    
    private static IEnumerator ProcessAllConfigurations(List<Configuration> splitConfigs)
    {
        foreach (var config in splitConfigs)
        {
            var optimiser = PrefabOptimiser.Run(config);
            if (optimiser == null) 
                continue;
            
            var asyncOp = Resources.UnloadUnusedAssets();
            while (!asyncOp.isDone)
                yield return null;
        }
    }
    
    private static List<Configuration> CreateSplitConfigurations(List<Configuration> configs)
    {
        var splitConfigs = new List<Configuration>();

        foreach (var config in configs)
        {
            if (!config.enabled)
                continue;

            if (config.superFolder)
            {
                var subFolders = Directory.GetDirectories(config.directoryFrom);
                foreach (var subFolder in subFolders)
                {
                    var newConfig = new Configuration(subFolder, $"{config.directoryTo}/{Path.GetFileName(subFolder)}", false)
                    {
                        enabled = config.enabled,
                        targetPrefabs = config.targetPrefabs,
                        targetScenes = config.targetScenes,
                        atlasName = config.atlasName
                    };
                    splitConfigs.Add(newConfig);
                }
            }
            else
                splitConfigs.Add(config);
        }

        return splitConfigs;
    }

    private static bool ValidateConfiguration(Configuration config)
    {
        if (string.IsNullOrEmpty(config.directoryFrom) || string.IsNullOrEmpty(config.directoryTo))
            return false;
        if (!Directory.Exists(config.directoryFrom))
            return false;
        if (config.directoryTo.StartsWith($"{config.directoryFrom}/"))
            return false;
        if (config.directoryFrom.StartsWith($"{config.directoryTo}/"))
            return false;
        if (!Directory.EnumerateFiles(config.directoryFrom, "*.prefab", SearchOption.AllDirectories).Any() &&
            !Directory.EnumerateFiles(config.directoryFrom, "*.fbx", SearchOption.AllDirectories).Any())
            return false;
        return true;
    }

    #endregion Private Methods
}
