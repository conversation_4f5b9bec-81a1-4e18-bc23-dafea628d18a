using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using TMPro;
using Unity.Burst;
using Unity.Collections;
using Unity.Mathematics;
using UnityEditor;
using UnityEditor.Animations;
using UnityEngine;
using static PrefabOptimiser;
using Object = UnityEngine.Object;
using OR = OptimiserReport;

public partial class MeshAtlasser
{
    private static readonly Type[] c_BlacklistedComponents = { typeof(TextMeshPro) };
    private static readonly Type[] c_BlacklistedParentComponents = { typeof(SnapHinge) };
    private static readonly string[] c_BlackListedMaterials = { "Shader Graphs/WaterStreamFalls" };
    
    private const string c_Albedo = "Albedo";
    private const string c_Normal = "Normal";
    private const string c_Tint = "Tint";
    private const string c_Mask = "Mask";
    private const string c_Emission = "Emission";
    private const string c_Control = "Control";

    private static MeshAtlasser Me;

    private string matFolder;
    private string meshFolder;
    private string atlasFolder;
    private string atlasName;
    
    private Dictionary<Material, ShaderVariant> m_shaderVariantCache = new();
    private Dictionary<SubMeshID, Material> m_matCache = new();
    private SubMeshID[] m_subMeshes;
    private Dictionary<Material, int> m_matInds = new();
    
    private struct ShaderVariant : IEquatable<ShaderVariant>
    {
        private readonly Shader shader;
        private Hash128 hash;

        public ShaderVariant(Material _mat)
        {
            shader = _mat.shader;
            hash = new Hash128();
            foreach (var keyword in _mat.shaderKeywords)
                hash.Append(keyword);
        }

        public override int GetHashCode() => shader.GetHashCode() ^ hash.GetHashCode();

        public override bool Equals(object obj) => obj is ShaderVariant other && Equals(other);
        
        public void Deconstruct(out Shader _shader, out Hash128 _hash)
        {
            _shader = shader;
            _hash = hash;
        }

        public override string ToString()
        {
            return $"{shader.name}{hash}";
        }

        [BurstDiscard] //No idea why but burst tries to compile this method, which is not burst compatible
        public bool Equals(ShaderVariant other)
        {
            return shader.Equals(other.shader) && hash.Equals(other.hash);
        }
    }

    private struct SubMeshID : IEquatable<SubMeshID>
    {
        private readonly MeshFilter meshFilter;
        private int subMeshIndex;

        public SubMeshID(MeshFilter _mF, int _sMI)
        {
            meshFilter = _mF;
            subMeshIndex = _sMI;
        }

        public override int GetHashCode() => meshFilter.GetHashCode() ^ subMeshIndex;

        public override bool Equals(object obj) => obj is SubMeshID other && Equals(other);
        
        public void Deconstruct(out MeshFilter _mF, out int _sMI)
        {
            _mF = meshFilter;
            _sMI = subMeshIndex;
        }

        public override string ToString()
        {
            return $"{meshFilter.gameObject.name}[{subMeshIndex}]";
        }

        public bool Equals(SubMeshID other)
        {
            return meshFilter.Equals(other.meshFilter) && subMeshIndex.Equals(other.subMeshIndex);
        }
    }

    private class MapType
    {
        public string TypeName;
        private readonly List<string> m_possibleNames;
        private readonly Color m_defaultColour;
        private readonly bool m_linear;

        private Dictionary<Shader, string> m_mapNames;

        public MapType(Color _default, bool _linear, string _mainName, params string[] _names)
        {
            TypeName = _mainName;

            m_defaultColour = _default;
            m_linear = _linear;
            m_possibleNames = new List<string>(_names);
            m_mapNames = new Dictionary<Shader, string>();
        }

        public bool Check(Shader _shader, string _texName)
        {
            if (!m_possibleNames.Contains(_texName))
                return false;
            if (m_mapNames.TryGetValue(_shader, out var oldName))
            {
                if (oldName == _texName)
                    return true;
                OR.LogError($"Found {TypeName} map {_texName} on shader {_shader} but already have {oldName}. Disregarding {_texName}", _shader);
                return true;
            }
            m_mapNames.Add(_shader, _texName);
            return true;
        }

        public string CheckTex(string _texName)
        {
            foreach (var alias in m_possibleNames)
            {
                if (_texName.Contains(alias))
                    return alias;
            }

            return "";
        }

        public Texture2D Get(Material _mat)
        {
            var tex = _mat.GetTexture(m_mapNames[_mat.shader]) as Texture2D;
            if (tex == null)
            {
                tex = new Texture2D(1, 1, TextureFormat.RGBA32, false, m_linear);
                tex.SetPixel(0, 0, m_defaultColour);
                tex.Apply();
            }
            return tex;
        }

        public void Set(Material _mat, Texture2D _tex)
        {
            if (m_mapNames.TryGetValue(_mat.shader, out var propertyName))
            {
                _mat.SetTexture(propertyName, _tex);
            }
            else
            {
                OR.LogWarning($"Cannot set texture for shader {_mat.shader.name} - property name not found for {TypeName}");
            }
        }
    }

    private MapType m_albedoMapType = new(Color.white, false, c_Albedo, "_MainTex", "_BaseMap");
    private MapType m_normalMapType = new(new Color(.5f, .5f, 1f, 1), true, c_Normal, "_BumpMap", "_NormalMap", "_PlantNormalMap");
    private MapType m_tintMapType = new(Color.white, true, c_Tint, "_TintMap");
    private MapType m_maskMapType = new(Color.white, true, c_Mask, "_MaskMap", "_Mask");
    private MapType m_emissionMapType = new(Color.black, true, c_Emission, "_EmissionMap", "_EmmisionMap", "_EmisiveMap");
    private MapType m_controlMapType = new(Color.white, true, c_Control, "_PlantControl");

    private class MapBundle
    {
        private readonly string m_shaderName;

        private readonly MapType[] m_allMapTypes;
        private readonly bool[] m_hasMapTypes;
        private readonly List<Texture2D>[] m_allMaps;
        private readonly List<string>[] m_mapPaths;
        private readonly int m_numMapTypes;

        private readonly List<int2> m_sizes = new();

        private List<SubMeshID> m_subMeshes = new();
        private List<Material> m_materials = new();
        private List<List<TriData>> m_allTriData = new();

        public MapBundle(ShaderVariant _sv)
        {
            var (shader, _hash) = _sv;
            m_shaderName = $"{shader.name.Split('/')[^1]}{_hash.GetHashCode()}";

            m_allMapTypes = new[]
            {
                Me.m_albedoMapType, Me.m_normalMapType, Me.m_tintMapType, Me.m_maskMapType, Me.m_emissionMapType,
                Me.m_controlMapType
            };
            m_numMapTypes = m_allMapTypes.Length;
            m_hasMapTypes = new bool[m_numMapTypes];
            m_allMaps = new List<Texture2D>[m_numMapTypes];
            m_mapPaths = new List<string>[m_numMapTypes];

            CheckAllMaps(shader);
            for (int i = 0; i < m_numMapTypes; ++i)
            {
                if (m_hasMapTypes[i])
                {
                    m_allMaps[i] = new List<Texture2D>();
                    m_mapPaths[i] = new List<string>();
                }
            }
        }

        private void CheckAllMaps(Shader _shader)
        {
            var propertyCount = ShaderUtil.GetPropertyCount(_shader);
            for (int i = 0; i < propertyCount; ++i)
            {
                if (ShaderUtil.GetPropertyType(_shader, i) != ShaderUtil.ShaderPropertyType.TexEnv)
                    continue;
                var name = ShaderUtil.GetPropertyName(_shader, i);
                var found = false;
                for (int j = 0; j < m_numMapTypes; ++j)
                {
                    bool check = m_allMapTypes[j].Check(_shader, name);
                    if (!check)
                        continue;
                    m_hasMapTypes[j] = true;
                    found = true;
                    break;
                }

                if (!found)
                    OR.LogWarning($"Shader {_shader.name} texture {name} not currently being atlassed");
            }
        }


        private void DoForAll(Action<MapType, List<Texture2D>, List<string>, int> Action)
        {
            int mapIndex = 0;
            for (int i = 0; i < m_numMapTypes; ++i)
            {
                if (m_hasMapTypes[i])
                    Action(m_allMapTypes[i], m_allMaps[i], m_mapPaths[i], mapIndex++);
            }
        }

        public void Add(Material _mat, MeshFilter _mF, int _sMI)
        {
            m_subMeshes.Add(new(_mF, _sMI));
            
            if (m_materials.Contains(_mat))
                return;
            
            m_materials.Add(_mat);
            DoForAll((type, maps, paths, _) =>
            {
                var originalTex = type.Get(_mat);
                maps.Add(GetReadableTexture(originalTex, type.TypeName));
                paths.Add(AssetDatabase.GetAssetPath(originalTex));
            });
        }

        public void ResizeAllMaps()
        {
            for (int i = 0; i < m_numMapTypes; ++i)
                m_hasMapTypes[i] = m_hasMapTypes[i] && m_allMaps[i].Count > 0;

            for (int i = 0; i < m_materials.Count; ++i)
            {
                var newMaps = new List<Texture2D>();
                var i1 = i;
                DoForAll((_, maps, _, _) => newMaps.Add(maps[i1]));
                m_sizes.Add(ResizeToMaxSize(newMaps));
                DoForAll((_, maps, _, ind) => maps[i1] = newMaps[ind]);
            }
        }

        private static int2 ResizeToMaxSize(List<Texture2D> _textures)
        {
            var maxSize = new int2(1, 1);
            foreach (var tex in _textures)
                maxSize = math.max(maxSize, new int2(tex.width, tex.height));

            for (var index = 0; index < _textures.Count; index++)
            {
                var tex = _textures[index];
                _textures[index] = UpscaleTexture(tex, maxSize);
            }
            return maxSize;
        }

        public bool ProcessMeshes()
        {
            for (int i = 0; i < m_subMeshes.Count; ++i)
            {
                var (mf, subMeshIndex) = m_subMeshes[i];
                var mat = Me.m_matCache[new(mf, subMeshIndex)];
                var mesh = mf.sharedMesh;
                var tris = GetMeshData(mesh, subMeshIndex, m_sizes[m_materials.IndexOf(mat)], i, out bool _skip);
                if (_skip)
                    m_subMeshes.RemoveAt(i--);
                else
                    m_allTriData.Add(tris);
            }

            return m_allTriData.Count > 1;
        }

        public void GenerateAndApply(DecorationHolder[][] subMeshDHs)
        {
            var channelMap = BuildChannelMap(subMeshDHs);

            var (texDict, firstOccs) = GroupTrianglesByMaterial(channelMap);

            var (packedRects, packedTris, keyList, size) = ComputeAndPackRects(texDict);
            if (packedRects == null)
                return;

            var atlasses = CreateAndStampAtlases(size, keyList, firstOccs, packedRects, channelMap);

            RebuildMeshesAndAssignMaterials(packedTris, atlasses, channelMap);
        }

        private Dictionary<Material, int[]> BuildChannelMap(DecorationHolder[][] subMeshDHs)
        {
            return new();
            //var dhMap = ComputeSubmeshInfluences(subMeshDHs);
            //return ColorConflictGraph(dhMap);
        }

        private (Dictionary<Hash128, List<TriData>>, Dictionary<Hash128, (int, Material)>) GroupTrianglesByMaterial(
            Dictionary<Material, int[]> channelMap)
        {
            var matHashes = HashMats(channelMap);
            var texDict = new Dictionary<Hash128, List<TriData>>();
            var firstOccs = new Dictionary<Hash128, (int, Material)>();
            for (int i = 0; i < m_subMeshes.Count; ++i)
            {
                var mat = Me.m_matCache[m_subMeshes[i]];
                var hash = matHashes[mat];
                if (!texDict.TryAdd(hash, new List<TriData>(m_allTriData[i])))
                    texDict[hash].AddRange(m_allTriData[i]);
                else
                    firstOccs.Add(hash, (i, mat));
            }

            return (texDict, firstOccs);
        }

        private (int4x2[][] packedRects, TriData[][] packedTris, List<Hash128> keyList, int2 size) ComputeAndPackRects(
            Dictionary<Hash128, List<TriData>> texDict)
        {
            var keyList = new List<Hash128>(texDict.Keys);
            var submeshRects = new List<List<(int4, List<TriData>)>>();
            foreach (var key in keyList)
                submeshRects.Add(GetMinimalCoveringRects(texDict[key], 0.06f));

            var (rects, tris) = BoxPack(submeshRects, out var size);
            if (size.x <= 0 || size.y <= 0)
            {
                OR.LogError("Atlas too large, split the folder");
                return (null, null, null, size);
            }

            return (rects, tris, keyList, size);
        }

        private List<Texture2D> CreateAndStampAtlases(int2 size, List<Hash128> keyList,
            Dictionary<Hash128, (int, Material)> firstOccs, int4x2[][] packedRects,
            Dictionary<Material, int[]> channelMap)
        {
            var atlasses = CreateAtlasses(size);
            long alphaPixels = 0, opaquePixels = 0;
            for (int i = 0; i < keyList.Count; i++)
            {
                var (_, mat) = firstOccs[keyList[i]];
                var chMap = channelMap.GetValueOrDefault(mat, new[] { 0, 1, 2, 3 });
                var pixelData = StampAllMaps(atlasses, packedRects[i], m_materials.IndexOf(mat), chMap);
                alphaPixels += pixelData.x;
                opaquePixels += pixelData.y;
            }

            var alphaRatio = alphaPixels / (alphaPixels + opaquePixels + float.Epsilon);
            RecreateAtlassesWithoutAlpha(atlasses, alphaRatio);
            SaveAllAtlasses(atlasses);
            if (alphaRatio is < 0.05f and > 0f)
                OR.LogWarning($"Alpha ratio {alphaRatio} is low, consider using opaque textures", atlasses[0]);
            CreateAtlasVisualisationData(atlasses, keyList, firstOccs, packedRects);
            return atlasses;
        }

        private void RebuildMeshesAndAssignMaterials(TriData[][] packedTris, List<Texture2D> atlasses,
            Dictionary<Material, int[]> channelMap)
        {
            Material baseMat = null;
            foreach (var tris in packedTris)
            {
                var sorted = SortTrisByMesh(tris);
                foreach (var (index, sortedTris) in sorted)
                {
                    var (mF, subIndex) = m_subMeshes[index];
                    
                    var mesh = Object.Instantiate(mF.sharedMesh);
                    RebuildMeshToAtlas(mesh, subIndex, sortedTris);
                    mF.mesh = mesh;
                    SaveAsset(mesh, Path.Combine(Me.meshFolder, $"{mF.name}({mF.GetInstanceID()}).asset"));

                    var originalMat = Me.m_matCache[new(mF, subIndex)];
                    AssignMaterialToRenderer(mF, subIndex, originalMat, atlasses, channelMap, ref baseMat);
                }
            }
        }
        
        private void AssignMaterialToRenderer(MeshFilter mf, int submeshIndex, Material originalMat,
            List<Texture2D> atlasses, Dictionary<Material,int[]> channelMap, ref Material baseMat)
        {
            var renderer = mf.GetComponent<MeshRenderer>();
            var mats = renderer.sharedMaterials;
            var mat = originalMat;

            if (channelMap.TryGetValue(originalMat, out var chMap))
            {
                mat = new(originalMat);
                var cols = new Color[4];
                var blends = new float[4];
                for (int i = 0; i < 4; i++)
                {
                    int j = chMap[i];
                    cols[i] = originalMat.GetColor(ColourName(j));
                    blends[i] = originalMat.GetFloat(BlendName(j));
                }
                for (int i = 0; i < 4; i++)
                {
                    mat.SetColor(ColourName(i), cols[i]);
                    mat.SetFloat(BlendName(i), blends[i]);
                }
            }

            if (baseMat == null)
            {
                baseMat = new(mat);
                FillInAtlasses(baseMat, atlasses);
                SaveAsset(baseMat, Path.Combine(Me.matFolder, $"{m_shaderName}{renderer.name}{submeshIndex}.mat"));
                mats[submeshIndex] = baseMat;
            }
            else
            {
                MaterialPropertyOverride.TryAdd(mat, baseMat, submeshIndex, mf.gameObject);
                mats[submeshIndex] = baseMat;
            }

            renderer.sharedMaterials = mats;
        }
        
        private List<(int submeshIndex, List<TriData> tris)> SortTrisByMesh(TriData[] tris)
        {
            var grouped = new Dictionary<int, List<TriData>>();
            foreach (var tri in tris)
            {
                if (!grouped.TryGetValue(tri.meshID, out var list))
                {
                    list = new List<TriData>();
                    grouped[tri.meshID] = list;
                }
                list.Add(tri);
            }
            return grouped.Select(kv => (kv.Key, kv.Value)).ToList();
        }

        private void CreateAtlasVisualisationData(List<Texture2D> atlasses, List<Hash128> keyList,
            Dictionary<Hash128, (int, Material)> firstOccs, int4x2[][] packedRects)
        {
            try
            {
                if (atlasses.Count == 0)
                    return;
                var visData = ScriptableObject.CreateInstance<AtlasVisualisationData>();
                var editorFolder = Path.Combine(Me.atlasFolder, "Editor");
                Directory.CreateDirectory(editorFolder.Replace("Assets", Application.dataPath));
                AssetDatabase.CreateAsset(visData,
                    Path.Combine(editorFolder, $"{Me.atlasName}_{m_shaderName}_VisData.asset"));
                visData.atlasTexture = atlasses[0];

                for (int i = 0; i < keyList.Count; i++)
                {
                    var (_, mat) = firstOccs[keyList[i]];
                    var matIndex = m_materials.IndexOf(mat);

                    var originalPath = m_mapPaths[0][matIndex];
                    foreach (var rectPair in packedRects[i])
                    {
                        var origRectInt = rectPair.c0;
                        var atlasRectInt = rectPair.c1;

                        var mapping = new AtlasRectMapping
                        {
                            originalTexturePath = originalPath,
                            originalPixelRect = new Rect(origRectInt.x, origRectInt.y,
                                origRectInt.z - origRectInt.x,
                                origRectInt.w - origRectInt.y),
                            atlasPixelRect = new Rect(atlasRectInt.x, atlasRectInt.y,
                                atlasRectInt.z - atlasRectInt.x,
                                atlasRectInt.w - atlasRectInt.y)
                        };
                        visData.mappings.Add(mapping);
                    }
                }

                // Calculate and store texel density data
                CalculateTexelDensityData(visData, atlasses[0]);

                EditorUtility.SetDirty(visData);
                AssetDatabase.SaveAssets();
            }
            catch (Exception e)
            {
                OR.LogException(e);
            }
        }

        private Dictionary<Material, Hash128> HashMats(Dictionary<Material, int[]> dhMap)
        {
            var matHashes = new Dictionary<Material, Hash128>();
            for (int i = 0; i < m_materials.Count; ++i)
            {
                var mat = m_materials[i];
                var hash = new Hash128();
                if (dhMap.TryGetValue(mat, out var mappings))
                {
                    foreach (var mapping in mappings)
                        hash.Append(mapping);
                }

                var i1 = i;
                DoForAll((_, maps, _, _) => hash.Append(maps[i1].imageContentsHash.ToString()));
                matHashes.Add(mat, hash);
            }

            return matHashes;
        }

        private int2 StampAllMaps(List<Texture2D> _atlasses, int4x2[] _rects, int _matIndex, int[] _channelMap)
        {
            int2 pixels = 0;
            DoForAll((type, maps, _, ind) =>
            {
                if (type == Me.m_tintMapType)
                {
                    CopyTextureChannels(maps[_matIndex], _atlasses[ind], _rects, _channelMap);
                }
                else if (type == Me.m_albedoMapType)
                    pixels = CopyTexture(maps[_matIndex], _atlasses[ind], _rects);
                else
                    CopyTexture(maps[_matIndex], _atlasses[ind], _rects);
            });
            return pixels;
        }

        private List<Texture2D> CreateAtlasses(int2 _size)
        {
            var atlasses = new List<Texture2D>();
            DoForAll((type, _, _, _) =>
                atlasses.Add(new Texture2D(_size.x, _size.y, TextureFormat.RGBA32, false, type.TypeName != c_Albedo)));
            return atlasses;
        }

        private void RecreateAtlassesWithoutAlpha(List<Texture2D> _atlasses, float _alphaRatio)
        {
            DoForAll((type, _, _, ind) =>
            {
                var format = GetFormat(type, _alphaRatio);
                if (_atlasses[ind].format != format)
                {
                    var newTex = new Texture2D(_atlasses[ind].width, _atlasses[ind].height, format, false);
                    CopyTextureToOpaque(_atlasses[ind], newTex);
                    Object.DestroyImmediate(_atlasses[ind]);
                    _atlasses[ind] = newTex;
                }
            });
        }

        private TextureFormat GetFormat(MapType _type, float _alphaRatio)
        {
            if (_type == Me.m_albedoMapType)
                return _alphaRatio > 0f ? TextureFormat.RGBA32 : TextureFormat.RGB24;
            return _type == Me.m_emissionMapType ? TextureFormat.RGB24 : TextureFormat.RGBA32;
        }

        private void FillInAtlasses(Material _mat, List<Texture2D> _atlasses)
        {
            DoForAll((type, _, _, ind) => type.Set(_mat, _atlasses[ind]));
        }

        private void SaveAllAtlasses(List<Texture2D> _atlasses)
        {
            StartSection("Texture Import");
            var paths = new string[_atlasses.Count];
            Directory.CreateDirectory(Me.atlasFolder);

            DoForAll((type, _, _, i) =>
            {
                var texPath = Path.Combine(Me.atlasFolder, $"{Me.atlasName}_{m_shaderName}_{type.TypeName}.png");
                paths[i] = texPath;

                var atlas = _atlasses[i];
                var rawData = atlas.GetRawTextureData<byte>();
                var width = (uint)atlas.width;
                var height = (uint)atlas.height;
                var format = atlas.graphicsFormat;
                var data = ImageConversion.EncodeNativeArrayToPNG(rawData, format, width, height);
                using (var fs = new FileStream(texPath, FileMode.Create, FileAccess.Write, FileShare.None, 65536, FileOptions.WriteThrough))
                {
                    fs.Write(data);
                }
                AssetDatabase.ImportAsset(texPath, ImportAssetOptions.ForceSynchronousImport);
            });
            
            AssetDatabase.StartAssetEditing();
            try
            {
                for (int i = 0; i < paths.Length; i++)
                {
                    var relativePath = GetRelativePath(paths[i]);
                    AssetDatabase.ImportAsset(relativePath, ImportAssetOptions.ForceSynchronousImport);
                    var importer = (TextureImporter)AssetImporter.GetAtPath(relativePath);
                    var type = m_allMapTypes[i];

                    bool needsReimport = importer.wrapMode != TextureWrapMode.Clamp ||
                                         importer.sRGBTexture != (type.TypeName == c_Albedo) ||
                                         importer.maxTextureSize != 8192 ||
                                         importer.textureCompression != TextureImporterCompression.Compressed;

                    if (!needsReimport)
                        continue;

                    TextureImporterSettings settings = new TextureImporterSettings();
                    importer.ReadTextureSettings(settings);
                    settings.wrapMode = TextureWrapMode.Clamp;
                    settings.sRGBTexture = (type.TypeName == c_Albedo);
                    importer.SetTextureSettings(settings);
                    importer.maxTextureSize = 8192;
                    importer.textureCompression = TextureImporterCompression.Compressed;
                    importer.SaveAndReimport();
                }
            }
            finally
            {
                AssetDatabase.StopAssetEditing();
            }

            DoForAll((type, _, _, i) =>
            {
                var atlas = AssetDatabase.LoadAssetAtPath<Texture2D>(GetRelativePath(paths[i]));
                _atlasses[i] = atlas;
                var width = atlas.width;
                var height = atlas.height;
                if (width * height > 4096 * 4096)
                    OR.LogWarning($"Atlas texture {type.TypeName} exceeds maximum recommended size of 4096x4096: {width}x{height}", atlas);
                OR.LogInfo($"Saved atlas texture: {type.TypeName} ({atlas.width}x{atlas.height}, {atlas.graphicsFormat})", atlas);
            });

            OR.LogInfo($"Successfully imported {_atlasses.Count} atlas textures for shader variant: {m_shaderName}");
            EndSection("Texture Import");
        }
        
        public static void FlipVertically(byte[] raw, int width, int height, int bytesPerPixel)
        {
            int rowBytes = width * bytesPerPixel;
            var tmp = new byte[rowBytes];

            for (int y = 0; y < height / 2; y++)
            {
                int topOff    = y              * rowBytes;
                int bottomOff = (height - 1 - y) * rowBytes;

                // swap top row <-> bottom row
                Buffer.BlockCopy(raw, topOff,    tmp,         0,       rowBytes);
                Buffer.BlockCopy(raw, bottomOff, raw,         topOff,  rowBytes);
                Buffer.BlockCopy(tmp,    0,      raw,         bottomOff, rowBytes);
            }
        }

        private Texture2D GetReadableTexture(Texture2D _source, string _mapType)
        {
            if (_source == null) return null;
            if (_source.isReadable) return _source;

            var readWriteMode = _source.isDataSRGB ? RenderTextureReadWrite.sRGB : RenderTextureReadWrite.Linear;
            RenderTexture tmp =
                RenderTexture.GetTemporary(_source.width, _source.height, 0, RenderTextureFormat.ARGB32, readWriteMode);
            Graphics.Blit(_source, tmp);
            RenderTexture previous = RenderTexture.active;
            RenderTexture.active = tmp;
            Texture2D result = new Texture2D(_source.width, _source.height);
            result.ReadPixels(new Rect(0, 0, tmp.width, tmp.height), 0, 0);
            result.Apply();
            RenderTexture.active = previous;
            RenderTexture.ReleaseTemporary(tmp);
            if (_mapType == c_Normal)
                MakeNormalMapReadable(result);
            if (_mapType != c_Albedo && _source.isDataSRGB)
            {
                FromSRGB(result);
                OR.LogWarning($"Map used as non-albedo {_source.name} is flagged as sRGB. Auto-converting", _source);
            }
            else if (_mapType == c_Albedo && !_source.isDataSRGB)
            {
                ToSRGB(result);
                OR.LogWarning($"Map used as albedo {_source.name} is flagged as linear. Auto-converting", _source);
            }
            CheckTexName(_source, _mapType);
            return result;
        }
    
        private void CheckTexName(Texture2D _tex, string _type)
        {
            var texName = _tex.name;
            foreach (var type in m_allMapTypes)
            {
                var typeName = type.TypeName;
                if (typeName == _type)
                    continue;
                
                var str = type.CheckTex(texName);
                if (str == "")
                    continue;
                OR.LogWarning($"Texture {texName} suggests it is a {typeName} type map but is being used as a {_type} type map!", _tex);
                break;
            }
        }
    }

    public static void Start(List<GameObject> _objs, string _matFolder, string _meshFolder, string _atlasFolder,
        string _atlasName = "Atlas")
    {
        Me = new MeshAtlasser
        {
            matFolder = _matFolder,
            meshFolder = _meshFolder,
            atlasFolder = _atlasFolder,
            atlasName = _atlasName
        };

        Me.AtlasPrefabs(_objs);
    }

    private void AtlasPrefabs(List<GameObject> objs)
    {
        var shaderMaps = new Dictionary<ShaderVariant, MapBundle>();

        StartSection("Texture Collection");
        m_subMeshes = CollectSubMeshes(objs);
        OR.LogInfo($"Collected {m_subMeshes.Length} submeshes from {objs.Count} prefabs");
        EndSection("Texture Collection");

        StartSection("Material Consolidation");
        var subMeshDHs = GetSubmeshDHs();

        foreach (var (mF, sMI) in m_subMeshes)
        {
            var mat = m_matCache[new(mF, sMI)];
            if (!m_matInds.ContainsKey(mat))
            {
                m_matInds[mat] = m_matInds.Count;
                m_shaderVariantCache[mat] = new ShaderVariant(mat);
            }

            var sv = m_shaderVariantCache[mat];
            if (!shaderMaps.TryGetValue(sv, out var bundle))
            {
                bundle = new MapBundle(sv);
                shaderMaps.Add(sv, bundle);
            }
            bundle.Add(mat, mF, sMI);
        }

        OR.LogInfo($"Grouped materials into {shaderMaps.Count} shader variants");
        OR.LogInfo($"Processing {m_matInds.Count} unique materials");
        EndSection("Material Consolidation");

        StartSection("Atlas Generation");
        int processedBundles = 0;
        foreach (var (sv, bundle) in shaderMaps)
        {
            OR.LogInfo($"Processing shader variant: {sv}");
            bundle.ResizeAllMaps();
            if (bundle.ProcessMeshes())
            {
                StartSection("UV Remapping");
                bundle.GenerateAndApply(subMeshDHs);
                EndSection("UV Remapping");
            }
            processedBundles++;
        }
        OR.LogInfo($"Generated {processedBundles} texture atlases");
        EndSection("Atlas Generation");
    }
    
    private SubMeshID[] CollectSubMeshes(List<GameObject> _objs)
    {
        var subMeshes = new List<SubMeshID>();
        int totalMeshFilters = 0, validSubmeshCount = 0;

        foreach (var obj in _objs)
        {
            var animatedMFs = new HashSet<Transform>();
            foreach (var animator in obj.GetComponentsInChildren<Animator>(true))
            {
                var clips = PrefabBatcher.GetAllClips(animator.runtimeAnimatorController as AnimatorController);
                foreach (var clip in clips)
                {
                    foreach (var binding in AnimationUtility.GetObjectReferenceCurveBindings(clip))
                    {
                        if (binding.type == typeof(MeshRenderer) && binding.propertyName.Contains("_UV_Offset"))
                        {
                            Transform target = PrefabBatcher.FindTransform(animator.transform, binding.path);
                            if (target != null) animatedMFs.Add(target);
                        }
                    }
                    
                }
            }
            foreach (var mF in obj.GetComponentsInChildren<MeshFilter>(true))
            {
                totalMeshFilters++;

                if (animatedMFs.Contains(mF.transform))
                {
                    OR.LogInfo($"Not atlasing {mF.gameObject.name} as it has an animated mesh filter");
                    continue;
                }

                if (IsBlacklisted(mF.transform))
                    continue;
                
                var mesh = mF.sharedMesh;
                if (mesh == null)
                {
                    OR.LogWarning($"Null mesh on {mF.gameObject.name}! Cannot atlas.");
                    continue;
                }

                if (mF.GetComponent<MeshRenderer>() is not { } rend)
                {
                    OR.LogWarning($"No MeshRenderer on {mF.gameObject.name}! Cannot atlas.");
                    continue;
                }

                if (rend.sharedMaterials == null || rend.sharedMaterials.Length < mesh.subMeshCount)
                {
                    OR.LogWarning($"Not enough materials on {mF.gameObject.name}! Cannot atlas.");
                    continue;
                }

                for (int i = 0; i < mesh.subMeshCount; i++)
                {
                    var mat = rend.sharedMaterials[i];
                    if (!IsMatValid(mat))
                    {
                        OR.LogInfo($"Material {mat.name} on {mF.gameObject.name} is not valid for atlasing. Skipping submesh {i}.");
                        continue;
                    }

                    subMeshes.Add(new(mF, i));
                    m_matCache[new(mF, i)] = mat;
                    validSubmeshCount++;
                }
            }
        }

        OR.LogInfo("Submesh collection statistics:");
        OR.LogInfo($"  - Total mesh filters found: {totalMeshFilters}");
        OR.LogInfo($"  - Skipped submeshes: {totalMeshFilters - validSubmeshCount}");
        OR.LogInfo($"  - Valid submeshes for atlasing: {validSubmeshCount}");

        return subMeshes.ToArray();
    }

    private DecorationHolder[][] GetSubmeshDHs()
    {
        var subMeshDHs = new DecorationHolder[m_subMeshes.Length][];
        for (int i = 0; i < m_subMeshes.Length; i++)
        {
            var (mF, sMI) = m_subMeshes[i];
            
            var mat = mF.GetComponent<MeshRenderer>().sharedMaterials[sMI];
            if (!mat.HasColor(Decoration.c_tint1ColourProperty))
                continue;
            
            subMeshDHs[i] = mF.GetComponentsInParent<DecorationHolder>(true);
        }
        return subMeshDHs;
    }

    private static bool IsMatValid(Material _mat)
    {
        if (_mat == null)
            return false;
                    
        if (c_BlackListedMaterials.Contains(_mat.shader.name))
            return false;

        if (_mat.HasVector("_Tiling"))
        {
            var tiling = _mat.GetVector("_Tiling");
            if (!Mathf.Approximately(tiling.x, 1) || !Mathf.Approximately(tiling.y, 1))
                return false;
        }

        if (_mat.HasFloat("_Parallax"))
        {
            var parallax = _mat.GetFloat("_Parallax");
            if (parallax != 0)
                 return false;
        }

        if (_mat.HasVector("_PlantRotate"))
        {
            return false; //This is set dynamically so unfortunately this material can't be atlassed
        }

        return true;
    }

    private static bool IsBlacklisted(Transform _t)
    {
        foreach (var comp in c_BlacklistedComponents)
        {
            if (_t.GetComponent(comp) == null) 
                continue;
            OR.LogInfo($"Not atlasing {_t.gameObject.name} as it has blacklisted component {comp.Name}");
            return true;
        }
        foreach (var comp in c_BlacklistedParentComponents)
        {
            if (_t.GetComponentInParent(comp) == null) 
                continue;
            OR.LogInfo($"Not atlasing {_t.gameObject.name} as its parent has blacklisted component {comp.Name}");
            return true;
        }
        return false;
    }

    public enum FreeRectChoiceHeuristic
    {
        BestShortSideFit,
        BestLongSideFit,
        BestAreaFit,
        BottomLeftRule
    }

    [BurstCompile]
    public struct MaxRectsBinPack
    {
        public NativeList<int4> freeRectangles;

        public MaxRectsBinPack(int width, int height, Allocator allocator)
        {
            freeRectangles = new NativeList<int4>(allocator);
            freeRectangles.Add(new int4(0, 0, width - 1, height - 1));
        }

        public void Dispose()
        {
            if (freeRectangles.IsCreated)
                freeRectangles.Dispose();
        }

        public bool Insert(int width, int height, FreeRectChoiceHeuristic method, out int4 result)
        {
            int bestScore1 = int.MaxValue;
            int bestScore2 = int.MaxValue;
            int bestRectIndex = -1;
            result = 0;

            for (int i = 0; i < freeRectangles.Length; i++)
            {
                int currentWidth = freeRectangles[i].z - freeRectangles[i].x;
                int currentHeight = freeRectangles[i].w - freeRectangles[i].y;

                for (int rotation = 0; rotation < 2; rotation++)
                {
                    int targetWidth = rotation == 1 ? height : width;
                    int targetHeight = rotation == 1 ? width : height;

                    if (currentWidth < targetWidth || currentHeight < targetHeight) 
                        continue;
                    
                    int score1 = 0, score2 = 0;
                    int leftoverHoriz = Math.Abs(currentWidth - targetWidth);
                    int leftoverVert = Math.Abs(currentHeight - targetHeight);

                    switch (method)
                    {
                        case FreeRectChoiceHeuristic.BestShortSideFit:
                            score1 = Math.Min(leftoverHoriz, leftoverVert);
                            score2 = Math.Max(leftoverHoriz, leftoverVert);
                            break;

                        case FreeRectChoiceHeuristic.BestLongSideFit:
                            score1 = Math.Max(leftoverHoriz, leftoverVert);
                            score2 = Math.Min(leftoverHoriz, leftoverVert);
                            break;

                        case FreeRectChoiceHeuristic.BestAreaFit:
                            score1 = (currentWidth * currentHeight) - (targetWidth * targetHeight);
                            score2 = 0;
                            break;

                        case FreeRectChoiceHeuristic.BottomLeftRule:
                            score1 = freeRectangles[i].y + targetHeight;
                            score2 = freeRectangles[i].x;
                            break;
                    }

                    if (score1 >= bestScore1 && (score1 != bestScore1 || score2 >= bestScore2)) 
                        continue;
                        
                    bestScore1 = score1;
                    bestScore2 = score2;
                    bestRectIndex = i;
                    int x = freeRectangles[i].x;
                    int y = freeRectangles[i].y;
                    
                    result = new int4(x, y, x + targetWidth, y + targetHeight);
                }
            }

            if (bestRectIndex == -1)
            {
                result = 0;
                return false;
            }

            PlaceRect(result);
            return true;
        }


        private void PlaceRect(int4 rect)
        {
            int count = freeRectangles.Length;
            for (int i = 0; i < count; i++)
            {
                if (SplitFreeNode(i, rect))
                {
                    freeRectangles[i--] = freeRectangles[^1];
                    freeRectangles.RemoveAt(freeRectangles.Length - 1);
                    count = freeRectangles.Length;
                }
            }
            PruneFreeList();
        }

        private bool SplitFreeNode(int index, int4 usedNode)
        {
            int4 freeNode = freeRectangles[index];
            if (usedNode.x >= freeNode.z || usedNode.z <= freeNode.x ||
                usedNode.y >= freeNode.w || usedNode.w <= freeNode.y)
                return false;

            if (usedNode.x < freeNode.z && usedNode.z > freeNode.x)
            {
                if (usedNode.y > freeNode.y && usedNode.y < freeNode.w)
                    freeRectangles.Add(new int4(freeNode.x, freeNode.y, freeNode.z, usedNode.y));

                if (usedNode.w < freeNode.w)
                    freeRectangles.Add(new int4(freeNode.x, usedNode.w, freeNode.z, freeNode.w));
            }

            if (usedNode.y < freeNode.w && usedNode.w > freeNode.y)
            {
                if (usedNode.x > freeNode.x && usedNode.x < freeNode.z)
                    freeRectangles.Add(new int4(freeNode.x, freeNode.y, usedNode.x, freeNode.w));

                if (usedNode.z < freeNode.z)
                    freeRectangles.Add(new int4(usedNode.z, freeNode.y, freeNode.z, freeNode.w));
            }

            return true;
        }

        private void PruneFreeList()
        {
            for (int i = 0; i < freeRectangles.Length; i++)
            {
                for (int j = i + 1; j < freeRectangles.Length; j++)
                {
                    if (IsContainedIn(freeRectangles[i], freeRectangles[j]))
                    {
                        freeRectangles[i] = freeRectangles[freeRectangles.Length - 1];
                        freeRectangles.RemoveAt(freeRectangles.Length - 1);
                        i--;
                        break;
                    }

                    if (IsContainedIn(freeRectangles[j], freeRectangles[i]))
                    {
                        freeRectangles[j] = freeRectangles[freeRectangles.Length - 1];
                        freeRectangles.RemoveAt(freeRectangles.Length - 1);
                        j--;
                    }
                }
            }
        }

        private bool IsContainedIn(int4 a, int4 b)
        {
            return a.x >= b.x && a.y >= b.y && a.z <= b.z && a.w <= b.w;
        }
    }

    public class RectItem
    {
        public int SubmeshIndex;
        public int4 OriginalRect;
        public List<TriData> Triangles;
    }

    private static (int4x2[][], TriData[][]) BoxPack(List<List<(int4, List<TriData>)>> subMeshRects, out int2 size)
    {
        int subMeshCount = subMeshRects.Count;
        int totalArea = 0;
        List<RectItem> items = new List<RectItem>();

        for (int i = 0; i < subMeshCount; i++)
        {
            foreach (var (rect, tris) in subMeshRects[i])
            {
                items.Add(new RectItem { SubmeshIndex = i, OriginalRect = rect, Triangles = tris });
                totalArea += AreaOf(rect);
            }
        }

        items.Sort((a, b) => AreaOf(b.OriginalRect).CompareTo(AreaOf(a.OriginalRect)));

        for (var areaLog = 12; areaLog <= 26; ++areaLog)
        {
            if (1 << areaLog < totalArea)
                continue;

            int max = math.min((areaLog >> 1) + 3, 14);
            for (int x = (areaLog + 1) >> 1; x <= max; ++x)
            {
                size = new int2(1 << x, 1 << areaLog - x);
                var inputRects = new List<int4>(items.Count);
                foreach (var t in items)
                    inputRects.Add(t.OriginalRect);

                var outputRects = RunBoxPackJob(inputRects, size, FreeRectChoiceHeuristic.BestShortSideFit);
                bool failed = outputRects.Count < inputRects.Count;

                if (failed)
                    continue;
                var outRects = new int4x2[subMeshCount][];
                var outTris = new TriData[subMeshCount][];
                var rectsList = new List<List<int4x2>>();
                var trisList = new List<List<TriData>>();
                for (int i = 0; i < subMeshCount; i++)
                {
                    rectsList.Add(new List<int4x2>());
                    trisList.Add(new List<TriData>());
                }

                for (int i = 0; i < items.Count; i++)
                {
                    RectItem item = items[i];
                    int4 origRect = item.OriginalRect;
                    int4 newBox = outputRects[i];

                    foreach (var tri in item.Triangles)
                    {
                        tri.TransformUVs(origRect, newBox, size);
                        trisList[item.SubmeshIndex].Add(tri);
                    }

                    rectsList[item.SubmeshIndex].Add(new int4x2(origRect, newBox));
                }

                for (int i = 0; i < subMeshCount; i++)
                {
                    outRects[i] = rectsList[i].ToArray();
                    outTris[i] = trisList[i].ToArray();
                }

                return (outRects, outTris);
            }
        }
        size = -1;
        return (null, null);
    }

    private static float Efficiency(int4 a, int4 b) => AreaOf(a) + AreaOf(b) - AreaOf(TotalBounds(a, b));
    private static int WidthOf(int4 a) => a.z - a.x;
    private static int HeightOf(int4 a) => a.w - a.y;
    private static int AreaOf(int4 a) => WidthOf(a) * HeightOf(a);
    private static int4 TotalBounds(int4 a, int4 b) => new(math.min(a.xy, b.xy), math.max(a.zw, b.zw));

    [BurstCompile]
    public struct TriData
    {
        const int c_borderSize = 4;

        public int triID;
        public int meshID;

        private int2 size;

        private float2 vert1;
        public float2 Vert1 => vert1 / size; //privates are in pixel space, getters return in uv space
        private float2 vert2;
        public float2 Vert2 => vert2 / size;
        private float2 vert3;
        public float2 Vert3 => vert3 / size;
        public int2 GetMinPixel => math.max((int2)math.min(vert1, math.min(vert2, vert3)) - c_borderSize, 0);
        public int2 GetMaxPixel => math.min((int2)math.max(vert1, math.max(vert2, vert3)) + c_borderSize, size - 1);

        public TriData(float2 uv0, float2 uv1, float2 uv2, int tri, int mesh, int2 texSize)
        {
            size = texSize;
            triID = tri;
            meshID = mesh;
            vert1 = uv0 * size;
            vert2 = uv1 * size;
            vert3 = uv2 * size;
        }

        public void TransformUVs(int4 oldBox, int4 newBox, int2 newMatSize)
        {
            size = newMatSize;

            var oldPos = (float2)oldBox.xy;
            var newPos = (float2)newBox.xy;
            var translation = newPos - oldPos;
            vert1 += translation;
            vert2 += translation;
            vert3 += translation;

            if (newBox.z - newBox.x != oldBox.z - oldBox.x)
            {
                // Texture was rotated 90 degrees in the atlas, so UVs need to be rotated to match
                vert1 = (vert1 - newPos).yx + newPos;
                vert2 = (vert2 - newPos).yx + newPos;
                vert3 = (vert3 - newPos).yx + newPos;
            }
        }
    }
}