using static ORTabUtilities;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using UnityEditor;
using UnityEditor.IMGUI.Controls;
using UnityEngine;

public class ORErrorTab : OptimiserReportTab
{
    private TreeViewState _treeState;
    private LogTreeView _treeView;
    private LogNode _rootLogNode;
    private LogNode _selectedNode;
    private OptimiserReport _cachedReport;
    private Vector2 _scrollPosition;

    private string _searchString = "";
    private string _previousSearchString = "";
    private bool _showInfo = true;
    private bool _showWarnings = true;
    private bool _showErrors = true;

    private struct LogEntry
    {
        public MessageType Type;
        public string Message;
        public Object Context;
    }

    private class LogNode
    {
        public string Name { get; }
        public string FullPath { get; }
        public LogNode Parent { get; set; }
        public Dictionary<string, LogNode> Children { get; } = new();
        public List<LogEntry> DirectLogs { get; } = new();
        public int ErrorCount { get; set; }
        public int WarningCount { get; set; }
        public int InfoCount { get; set; }
        public int FilteredErrorCount { get; set; }
        public int FilteredWarningCount { get; set; }
        public int FilteredInfoCount { get; set; }
        public bool HasFilter { get; set; }

        public LogNode(string name, string fullPath)
        {
            Name = name;
            FullPath = fullPath;
        }

        public IEnumerable<LogEntry> GetAllLogs()
        {
            foreach (var log in DirectLogs)
            {
                yield return log;
            }
            foreach (var child in Children.Values)
            {
                foreach (var log in child.GetAllLogs())
                {
                    yield return log;
                }
            }
        }
    }


    private void OnEnable()
    {
        m_title = "Logs & Errors";
    }

    public override void Draw(OptimiserReport report, OptimiserReportEditor editor)
    {
        if (report != _cachedReport || _rootLogNode == null)
        {
            BuildDataStructures(report);
            _cachedReport = report;
        }

        if (_rootLogNode == null || (_rootLogNode.ErrorCount == 0 && _rootLogNode.WarningCount == 0 && _rootLogNode.InfoCount == 0))
        {
            EditorGUILayout.HelpBox("No logs, warnings, or errors were recorded during this process.", MessageType.Info);
            return;
        }
        
        EditorGUILayout.BeginHorizontal(EditorStyles.toolbar);
        _searchString = EditorGUILayout.TextField(_searchString, GUI.skin.FindStyle("ToolbarSearchTextField"), GUILayout.ExpandWidth(true));
        if (_searchString != _previousSearchString)
        {
            CalculateFilteredCounts(_rootLogNode, _searchString);
            _treeView.Reload();
            _previousSearchString = _searchString;
        }
        _showInfo = GUILayout.Toggle(_showInfo, "Info", EditorStyles.toolbarButton);
        _showWarnings = GUILayout.Toggle(_showWarnings, "Warnings", EditorStyles.toolbarButton);
        _showErrors = GUILayout.Toggle(_showErrors, "Errors", EditorStyles.toolbarButton);
        EditorGUILayout.EndHorizontal();

        float halfWidth = Mathf.Floor(EditorGUIUtility.currentViewWidth * 0.5f) - 20;

        EditorGUILayout.BeginHorizontal();

        var panelGUIStyle = new GUIStyle(GUI.skin.box) { fixedWidth = halfWidth, fixedHeight = 0 };
        // Left Panel: TreeView for log sections
        GUILayout.BeginVertical(panelGUIStyle);
        Rect treeRect = GUILayoutUtility.GetRect(0, 10000, 15, Mathf.Min(600, _treeView.totalHeight + 15));
        _treeView.OnGUI(treeRect);
        GUILayout.EndVertical();

        // Right Panel: Log details
        GUILayout.BeginVertical(panelGUIStyle, GUILayout.MaxHeight(600));
        DrawDetailsPanel();
        GUILayout.EndVertical();

        EditorGUILayout.EndHorizontal();
    }

    private void DrawDetailsPanel()
    {
        if (_selectedNode != null)
        {
            EditorGUILayout.LabelField(_selectedNode.Name, EditorStyles.boldLabel);
            if (GUILayout.Button("Log to Console", GUILayout.ExpandWidth(false)))
            {
                foreach (var log in _selectedNode.GetAllLogs())
                {
                    switch (log.Type)
                    {
                        case MessageType.Error:
                            Debug.LogError(log.Message, log.Context);
                            break;
                        case MessageType.Warning:
                            Debug.LogWarning(log.Message, log.Context);
                            break;
                        case MessageType.Info:
                            Debug.Log(log.Message, log.Context);
                            break;
                    }
                }
            }

            _scrollPosition = EditorGUILayout.BeginScrollView(_scrollPosition, GUILayout.ExpandHeight(true));
            
            var logs = _selectedNode.GetAllLogs();
            
            var filteredLogs = logs.Where(log =>
            {
                if (!_showInfo && log.Type == MessageType.Info) return false;
                if (!_showWarnings && log.Type == MessageType.Warning) return false;
                if (!_showErrors && log.Type == MessageType.Error) return false;
                if (string.IsNullOrEmpty(_searchString)) return true;
                try
                {
                    return Regex.IsMatch(log.Message, _searchString, RegexOptions.IgnoreCase);
                }
                catch (System.ArgumentException)
                {
                    return true;
                }
            }).ToList();
            
            if (filteredLogs.Any())
            {
                foreach (var log in filteredLogs)
                {
                    EditorGUILayout.HelpBox(log.Message, log.Type);
                    Rect lastRect = GUILayoutUtility.GetLastRect();
                    if (Event.current.type != EventType.MouseDown || Event.current.button != 0) continue;
                    if (!lastRect.Contains(Event.current.mousePosition)) continue;
                    EditorGUIUtility.PingObject(log.Context);
                    Event.current.Use();
                }
            }
            else
            {
                EditorGUILayout.LabelField("No logs match the current filter.", EditorStyles.centeredGreyMiniLabel);
            }
            
            EditorGUILayout.EndScrollView();
        }
        else
        {
            EditorGUILayout.HelpBox("Select a section in the tree to view its logs.", MessageType.Info);
        }
    }
    
    private void OnTreeSelectionChanged(List<int> selectedIds)
    {
        if (selectedIds.Count > 0)
        {
            var selectedItem = _treeView.FindItemById(selectedIds[0]);
            _selectedNode = selectedItem?.Data;
        }
        else
        {
            _selectedNode = null;
        }
    }

    private void BuildDataStructures(OptimiserReport report)
    {
        var timeInfosWithLogs = report.sectionInfos.Where(t => t.errors != null && t.errors.Any()).ToList();
        if (!timeInfosWithLogs.Any())
        {
            _rootLogNode = null;
            return;
        }
        
        var rootInfo = report.sectionInfos.FirstOrDefault(t => !t.section.Contains('/'));
        _rootLogNode = new LogNode(rootInfo.section, rootInfo.section);

        foreach (var timeInfo in timeInfosWithLogs)
        {
            var parts = timeInfo.section.Split('/');
            var currentNode = _rootLogNode;

            for (int i = 1; i < parts.Length; i++)
            {
                var part = parts[i];
                if (!currentNode.Children.TryGetValue(part, out var childNode))
                {
                    var fullPath = string.Join("/", parts.Take(i + 1));
                    childNode = new LogNode(part, fullPath) { Parent = currentNode };
                    currentNode.Children[part] = childNode;
                }
                currentNode = childNode;
            }

            foreach (var (level, message, context) in timeInfo.errors)
            {
                var messageType = level switch
                {
                    0 or 1 => MessageType.Error,
                    2 => MessageType.Warning,
                    3 => MessageType.Info,
                    _ => MessageType.None
                };
                currentNode.DirectLogs.Add(new LogEntry { Type = messageType, Message = message, Context = context });
            }
        }
        
        CalculateInclusiveCounts(_rootLogNode);

        _treeState ??= new TreeViewState();
        _treeView = new LogTreeView(_treeState, _rootLogNode);
        _treeView.OnSelectionChanged += OnTreeSelectionChanged;
        _treeView.Reload();
    }
    
    private void CalculateFilteredCounts(LogNode node, string searchString)
    {
        if (string.IsNullOrEmpty(searchString))
        {
            node.HasFilter = false;
            foreach (var child in node.Children.Values)
                CalculateFilteredCounts(child, searchString);
            return;
        }

        node.HasFilter = true;

        System.Func<string, bool> isMatch;
        try
        {
            isMatch = message => Regex.IsMatch(message, searchString, RegexOptions.IgnoreCase);
        }
        catch (System.ArgumentException) // Invalid regex pattern
        {
            isMatch = _ => true; 
        }

        node.FilteredInfoCount = node.DirectLogs.Count(l => l.Type == MessageType.Info && isMatch(l.Message));
        node.FilteredWarningCount = node.DirectLogs.Count(l => l.Type == MessageType.Warning && isMatch(l.Message));
        node.FilteredErrorCount = node.DirectLogs.Count(l => l.Type == MessageType.Error && isMatch(l.Message));

        foreach (var child in node.Children.Values)
        {
            CalculateFilteredCounts(child, searchString);
            node.FilteredInfoCount += child.FilteredInfoCount;
            node.FilteredWarningCount += child.FilteredWarningCount;
            node.FilteredErrorCount += child.FilteredErrorCount;
        }
    }

    private void CalculateInclusiveCounts(LogNode node)
    {
        var childrenToRemove = new List<string>();
        foreach (var child in node.Children)
        {
            CalculateInclusiveCounts(child.Value);
            if (child.Value.ErrorCount == 0 && child.Value.WarningCount == 0 && child.Value.InfoCount == 0)
            {
                childrenToRemove.Add(child.Key);
            }
        }
        foreach (var key in childrenToRemove)
        {
            node.Children.Remove(key);
        }

        node.InfoCount = node.DirectLogs.Count(l => l.Type == MessageType.Info);
        node.WarningCount = node.DirectLogs.Count(l => l.Type == MessageType.Warning);
        node.ErrorCount = node.DirectLogs.Count(l => l.Type == MessageType.Error);

        foreach (var child in node.Children.Values)
        {
            node.InfoCount += child.InfoCount;
            node.WarningCount += child.WarningCount;
            node.ErrorCount += child.ErrorCount;
        }
    }

    private class LogTreeView : BaseReportTreeView<LogNode>
    {
        public LogTreeView(TreeViewState state, LogNode root) : base(state, root)
        {
            showAlternatingRowBackgrounds = true;
            Reload();
        }

        protected override TreeViewItem BuildRoot()
        {
            var root = new ReportTreeViewItem<LogNode> { id = 0, depth = -1, displayName = "Root", Data = rootData };

            var rootItem = CreateItem(rootData);
            root.AddChild(rootItem);
        
            AddChildrenRecursive(rootData, rootItem);
        
            SetupDepthsFromParentsAndChildren(root);
            return root;
        }

        private void AddChildrenRecursive(LogNode node, TreeViewItem parent)
        {
            foreach (var childNode in node.Children.Values)
            {
                var item = CreateItem(childNode);
                parent.AddChild(item);
                AddChildrenRecursive(childNode, item);
            }
        }

        /// <summary>
        /// Helper to create a TreeViewItem with a formatted name including log counts.
        /// </summary>
        private ReportTreeViewItem<LogNode> CreateItem(LogNode node)
        {
            var counts = new List<string>();
            if (node.ErrorCount > 0)
            {
                var countStr = node.HasFilter ? $"{node.FilteredErrorCount}/{node.ErrorCount}" : node.ErrorCount.ToString();
                counts.Add($"{countStr} \u274c");
            }
            if (node.WarningCount > 0)
            {
                var countStr = node.HasFilter ? $"{node.FilteredWarningCount}/{node.WarningCount}" : node.WarningCount.ToString();
                counts.Add($"{countStr} \u26a0\ufe0f");
            }
            if (node.InfoCount > 0)
            {
                var countStr = node.HasFilter ? $"{node.FilteredInfoCount}/{node.InfoCount}" : node.InfoCount.ToString();
                counts.Add($"{countStr} ℹ\ufe0f");
            }

            string displayName = counts.Any() ? $"{node.Name} ({string.Join(", ", counts)})" : node.Name;

            return new ReportTreeViewItem<LogNode>
            {
                id = node.FullPath.GetHashCode(),
                displayName = displayName,
                Data = node
            };
        }
    }
}