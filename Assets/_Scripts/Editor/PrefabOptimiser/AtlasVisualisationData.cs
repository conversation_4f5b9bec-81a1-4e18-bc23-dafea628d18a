using UnityEngine;
using System.Collections.Generic;

[System.Serializable]
public class AtlasRectMapping
{
    public string originalTexturePath;
    public Rect originalPixelRect;
    public Rect atlasPixelRect;
}

[System.Serializable]
public class TexelDensityInfo
{
    public float minTexelsPerMeter;
    public float maxTexelsPerMeter;
    public float meanTexelsPerMeter;
    public List<MeshUsageInfo> meshUsages = new();
}

[System.Serializable]
public class MeshUsageInfo
{
    public string meshName;
    public int meshInstanceId;
    public float texelsPerMeter;
}

public class AtlasVisualisationData : ScriptableObject
{
    public Texture2D atlasTexture;
    public List<AtlasRectMapping> mappings = new();

    // Heatmap data - stored as flat arrays for each atlas pixel
    public float[] pixelMinTexelsPerMeter;
    public float[] pixelMaxTexelsPerMeter;
    public float[] pixelMeanTexelsPerMeter;
    public List<TexelDensityInfo> pixelDensityInfo = new();
}