using UnityEngine;
using System.Collections.Generic;
using Unity.Mathematics;

[System.Serializable]
public class AtlasRectMapping
{
    public string originalTexturePath;
    public Rect originalPixelRect;
    public Rect atlasPixelRect;
}

[System.Serializable]
public class MeshTriangleData
{
    public string meshName;
    public int meshInstanceId;
    public string meshAssetPath;
    public int triangleId;
    public int subMeshIndex;
    public float texelsPerMeter;

    // Triangle vertices in atlas UV space (0-1)
    public Vector2 uv0;
    public Vector2 uv1;
    public Vector2 uv2;

    // Original mesh triangle vertices (for world space calculations)
    public Vector3 worldVertex0;
    public Vector3 worldVertex1;
    public Vector3 worldVertex2;
}

public class AtlasVisualisationData : ScriptableObject
{
    public Texture2D atlasTexture;
    public List<AtlasRectMapping> mappings = new();

    // Store triangle data for on-demand heatmap generation
    public List<MeshTriangleData> triangleData = new();
}