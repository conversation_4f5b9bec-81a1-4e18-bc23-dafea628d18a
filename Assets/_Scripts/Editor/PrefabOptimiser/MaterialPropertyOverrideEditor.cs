using UnityEngine;
using UnityEditor;
using UnityEngine.Rendering;

[CustomEditor(typeof(MaterialPropertyOverride))]
public class MaterialPropertyOverrideEditor : Editor
{
    MaterialPropertyOverride mpo;
    
    void OnEnable()
    {
        mpo = (MaterialPropertyOverride)target;
    }
    
    public override void OnInspectorGUI()
    {
        DrawDefaultInspector();
        
        EditorGUI.BeginChangeCheck();
        bool newValue = EditorGUILayout.Toggle("Apply Editor Preview", mpo.EditorPreview);
        if (EditorGUI.EndChangeCheck())
        {
            mpo.EditorPreview = newValue;
            mpo.ApplyEditorPreview();
        }

        if (mpo.m_propertyOverrides is not { Count: > 0 })
            return;
        
        EditorGUILayout.Space();
        EditorGUILayout.BeginVertical("box");
        EditorGUILayout.LabelField($"Property Overrides ({mpo.m_propertyOverrides.Count})", EditorStyles.boldLabel);

        var rend = mpo.GetComponent<Renderer>();
        var mat = rend != null && rend.sharedMaterials.Length > mpo.m_subMeshIndex
            ? rend.sharedMaterials[mpo.m_subMeshIndex] : null;
        var shader = mat != null ? mat.shader : null;

        using (new EditorGUI.DisabledScope(true))
        {
            foreach (var propertyOverride in mpo.m_propertyOverrides)
            {
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField(propertyOverride.m_name, GUILayout.Width(150));

                if (shader != null)
                {
                    int idx = shader.FindPropertyIndex(propertyOverride.m_name);
                    if (idx >= 0)
                    {
                        var type = shader.GetPropertyType(idx);
                        switch (type)
                        {
                            case ShaderPropertyType.Color:
                                EditorGUILayout.ColorField(GUIContent.none, propertyOverride.m_overrideC);
                                break;
                            case ShaderPropertyType.Vector:
                                EditorGUILayout.Vector4Field(GUIContent.none, propertyOverride.m_overrideV);
                                break;
                            case ShaderPropertyType.Float:
                            case ShaderPropertyType.Range:
                                EditorGUILayout.FloatField(GUIContent.none, propertyOverride.m_overrideF);
                                break;
                            case ShaderPropertyType.Int:
                                EditorGUILayout.IntField(GUIContent.none, propertyOverride.m_overrideI);
                                break;
                        }
                    }
                }
                EditorGUILayout.EndHorizontal();
            }
        }

        EditorGUILayout.EndVertical();
    }
}