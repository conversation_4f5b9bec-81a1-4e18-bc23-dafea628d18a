//#define HOVER_RECT

using System.Collections.Generic;
using System.Linq;
using UnityEditor;
using UnityEngine;

public class AtlasVisualiserWindow : EditorWindow
{
    public AtlasVisualisationData visualizationData;

    private AtlasRectMapping selectedMapping;
    private List<AtlasRectMapping> relatedMappings;
    private AtlasRectMapping hoverMapping;

    private Texture2D originalTexture;
    private Texture2D heatmapTexture;

    private Vector2 leftPanelScroll;
    private Vector2 rightPanelScroll;

    private GUIStyle outlineStyle;

    // Heatmap mode variables
    private bool isHeatmapMode = false;
    private int heatmapDisplayMode = 0; // 0 = Max, 1 = Min, 2 = Mean
    private readonly string[] heatmapModeNames = { "Max", "Min", "Mean" };
    private Vector2 selectedPixelCoord = Vector2.zero;
    private bool hasSelectedPixel = false;

    [MenuItem("Art Tools/Atlas Visualiser")]
    public static void ShowWindow()
    {
        GetWindow<AtlasVisualiserWindow>("Atlas Visualiser");
    }

    private void OnEnable()
    {
        outlineStyle = new GUIStyle { normal = { background = EditorGUIUtility.whiteTexture } };

        Selection.selectionChanged += CheckSelected;
    }
    
    private void OnDisable()
    {
        Selection.selectionChanged -= CheckSelected;
    }

    private void CheckSelected()
    {
        if (Selection.activeObject is AtlasVisualisationData data)
        {
            if (visualizationData != data)
            {
                visualizationData = data;
                selectedMapping = null;
                relatedMappings = new List<AtlasRectMapping>();
                originalTexture = null;
                heatmapTexture = null;
                hasSelectedPixel = false;
                Repaint();
            }
        }
    }

    void OnGUI()
    {
        EditorGUILayout.LabelField("Atlas Data", EditorStyles.boldLabel);
        var oldVisualizationData = visualizationData;
        visualizationData = (AtlasVisualisationData)EditorGUILayout.ObjectField(visualizationData, typeof(AtlasVisualisationData), false);
        if (oldVisualizationData != visualizationData)
        {
            selectedMapping = null;
            relatedMappings = new List<AtlasRectMapping>();
            originalTexture = null;
            heatmapTexture = null;
            hasSelectedPixel = false;
        }

        EditorGUILayout.Space();

        // Mode selection
        EditorGUILayout.BeginHorizontal();
        var newIsHeatmapMode = EditorGUILayout.Toggle("Heatmap", isHeatmapMode, GUILayout.Width(60));
        if (newIsHeatmapMode != isHeatmapMode)
        {
            isHeatmapMode = newIsHeatmapMode;
            heatmapTexture = null;
            hasSelectedPixel = false;
        }

        if (isHeatmapMode)
        {
            EditorGUILayout.LabelField("Metric:", GUILayout.Width(50));
            var newHeatmapDisplayMode = EditorGUILayout.Popup(heatmapDisplayMode, heatmapModeNames, GUILayout.Width(80));
            if (newHeatmapDisplayMode != heatmapDisplayMode)
            {
                heatmapDisplayMode = newHeatmapDisplayMode;
                heatmapTexture = null;
            }
        }
        EditorGUILayout.EndHorizontal();

        EditorGUILayout.Space();

        if (visualizationData == null)
        {
            EditorGUILayout.HelpBox("Please assign an AtlasVisualizationData asset to begin.", MessageType.Info);
            return;
        }

        EditorGUILayout.BeginHorizontal(GUILayout.ExpandHeight(true));

        DrawLeftPanel();
        DrawRightPanel();
        
        EditorGUILayout.EndHorizontal();
    }

    private void DrawLeftPanel()
    {
        EditorGUILayout.BeginVertical(EditorStyles.helpBox, GUILayout.Width(position.width / 2));
        leftPanelScroll = EditorGUILayout.BeginScrollView(leftPanelScroll);

        if (visualizationData.atlasTexture != null)
        {
            float halfW = position.width / 2 - 20;
            float aspect = (float)visualizationData.atlasTexture.width / visualizationData.atlasTexture.height;
            Rect layoutRect = GUILayoutUtility.GetRect(halfW, halfW / aspect);

            Rect displayRect = GetAspectFitRect(
                new Vector2(visualizationData.atlasTexture.width, visualizationData.atlasTexture.height),
                layoutRect
            );

            // Draw either original atlas or heatmap
            if (isHeatmapMode)
            {
                if (heatmapTexture == null)
                    GenerateHeatmapTexture();

                if (heatmapTexture != null)
                    GUI.DrawTexture(displayRect, heatmapTexture, ScaleMode.ScaleToFit);
            }
            else
            {
                GUI.DrawTexture(displayRect, visualizationData.atlasTexture, ScaleMode.ScaleToFit);
            }

            if (selectedMapping != null)
            {
                foreach (var related in relatedMappings)
                {
                    var relRect = GetScaledRect(related.atlasPixelRect,
                        new Rect(0, 0,
                            visualizationData.atlasTexture.width,
                            visualizationData.atlasTexture.height),
                        displayRect);
                    DrawOutline(relRect, Color.grey);
                }
                var selRect = GetScaledRect(selectedMapping.atlasPixelRect,
                    new Rect(0, 0,
                        visualizationData.atlasTexture.width,
                        visualizationData.atlasTexture.height),
                    displayRect);
                DrawOutline(selRect, Color.red, 2);
            }
#if HOVER_RECT
            if (hoverMapping != null && hoverMapping != selectedMapping)
            {
                var hovRect = GetScaledRect(hoverMapping.atlasPixelRect,
                    new Rect(0, 0,
                        visualizationData.atlasTexture.width,
                        visualizationData.atlasTexture.height),
                    displayRect);
                DrawOutline(hovRect, new Color(1f, 1f, 0f, 0.5f), 2, 0f);
            }
#endif

            var e = Event.current;
            if (displayRect.Contains(e.mousePosition))
            {
                if (e.type == EventType.MouseDown)
                {
                    if (isHeatmapMode)
                        HandleHeatmapClick(e.mousePosition, displayRect);
                    else
                        HandleMouseOver(e.mousePosition, true, displayRect);
                    e.Use();
                }
                else
                {
                    if (!isHeatmapMode)
                        HandleMouseOver(e.mousePosition, false, displayRect);
                }
            }
        }
        else
        {
            EditorGUILayout.HelpBox("Please assign an AtlasVisualizationData asset to begin.", MessageType.Info);
        }

        EditorGUILayout.EndScrollView();
        EditorGUILayout.EndVertical();
    }

    private Rect GetAspectFitRect(Vector2 srcSize, Rect target)
    {
        float srcAspect = srcSize.x / srcSize.y;
        float tgtAspect = target.width / target.height;

        if (tgtAspect > srcAspect)
        {
            // letterbox horizontally
            float h = target.height;
            float w = h * srcAspect;
            float x = target.x + (target.width - w) * 0.5f;
            return new Rect(x, target.y, w, h);
        }
        else
        {
            // letterbox vertically
            float w = target.width;
            float h = w / srcAspect;
            float y = target.y + (target.height - h) * 0.5f;
            return new Rect(target.x, y, w, h);
        }
    }


    private void DrawRightPanel()
    {
        EditorGUILayout.BeginVertical(EditorStyles.helpBox, GUILayout.Width(position.width / 2));
        rightPanelScroll = EditorGUILayout.BeginScrollView(rightPanelScroll);

        if (isHeatmapMode)
        {
            DrawHeatmapInfo();
        }
        else
        {
            EditorGUILayout.LabelField("Original Texture", EditorStyles.centeredGreyMiniLabel);

            if (selectedMapping == null)
            {
                EditorGUILayout.HelpBox("Click a region on the atlas to see the original texture here.", MessageType.Info);
            }
        else if (originalTexture == null)
        {
             EditorGUILayout.HelpBox($"Could not load texture from path:\n{selectedMapping.originalTexturePath}", MessageType.Warning);
        }
        else
        {
            EditorGUILayout.LabelField(selectedMapping.originalTexturePath, EditorStyles.wordWrappedMiniLabel);
            
            float aspectRatio = (float)originalTexture.width / originalTexture.height;
            Rect originalDisplayRect = GUILayoutUtility.GetRect(position.width / 2 - 20, (position.width / 2 - 20) / aspectRatio);

            GUI.DrawTexture(originalDisplayRect, originalTexture, ScaleMode.ScaleToFit);

            if (GUI.Button(originalDisplayRect, GUIContent.none, GUIStyle.none))
            {
                Selection.activeObject = originalTexture;
                EditorGUIUtility.PingObject(originalTexture);
            }

            Rect scaledOutlineRect = GetScaledRect(selectedMapping.originalPixelRect, new Rect(0, 0, originalTexture.width, originalTexture.height), originalDisplayRect);
            DrawOutline(scaledOutlineRect, Color.red, 2);
            foreach (var related in relatedMappings)
            {
                Rect relatedRect = GetScaledRect(related.originalPixelRect, new Rect(0, 0, originalTexture.width, originalTexture.height), originalDisplayRect);
                DrawOutline(relatedRect, Color.grey);
            }
        }
        }

        EditorGUILayout.EndScrollView();
        EditorGUILayout.EndVertical();
    }

    private void HandleMouseOver(Vector2 mousePosition, bool isClick, Rect displayRect)
    {
        hoverMapping = null;

        foreach (var m in visualizationData.mappings)
        {
            Rect scaled = GetScaledRect(
                m.atlasPixelRect,
                new Rect(0, 0, visualizationData.atlasTexture.width, visualizationData.atlasTexture.height),
                displayRect
            );

            if (scaled.Contains(mousePosition))
            {
                if (isClick)
                {
                    selectedMapping = m;
                    relatedMappings = new List<AtlasRectMapping>();
                    foreach (var map in visualizationData.mappings)
                    {
                        if (map != m && map.originalTexturePath == m.originalTexturePath)
                            relatedMappings.Add(map);
                    }
                    originalTexture = LoadOriginalTexture(m.originalTexturePath);
                }
                else
                {
                    hoverMapping = m;
                }

                Repaint();
                return;
            }
        }

        if (!isClick)
        {
            hoverMapping = null;
            Repaint();
        }
    }
    
    private Texture2D LoadOriginalTexture(string path)
    {
        return string.IsNullOrEmpty(path) ? Texture2D.whiteTexture : AssetDatabase.LoadAssetAtPath<Texture2D>(path);
    }

    private Rect GetScaledRect(Rect sourceRect, Rect sourceTexRect, Rect displayRect)
    {
        float sx = displayRect.width  / sourceTexRect.width;
        float sy = displayRect.height / sourceTexRect.height;

        float flippedY = sourceTexRect.height - sourceRect.y - sourceRect.height;
        return new Rect(displayRect.x + sourceRect.x * sx, displayRect.y + flippedY * sy,
            sourceRect.width * sx, sourceRect.height * sy);
    }

    private void DrawOutline(Rect r, Color col, int border = 1, float fillAlpha = 0.25f)
    {
        var xMin = Mathf.FloorToInt(r.xMin);
        var xMax = Mathf.CeilToInt(r.xMax);
        var yMin = Mathf.FloorToInt(r.yMin);
        var yMax = Mathf.CeilToInt(r.yMax);
        var newXMin = xMin - border;
        var newXMax = xMax + border;
        var newYMin = yMin - border;
        var newYMax = yMax + border;

        EditorGUI.DrawRect(new Rect(newXMin, yMax, xMax - newXMin, newYMax - yMax), col);
        EditorGUI.DrawRect(new Rect(xMin, newYMin, newXMax - xMin, yMin - newYMin), col);
        EditorGUI.DrawRect(new Rect(newXMin, newYMin, xMin - newXMin, yMax - newYMin), col);
        EditorGUI.DrawRect(new Rect(xMax, yMin, newXMax - xMax, newYMax - yMin), col);
        if (fillAlpha > 0f)
        {
            Color fillColor = col;
            fillColor.a = fillAlpha;
            EditorGUI.DrawRect(new Rect(xMin, yMin, xMax - xMin, yMax - yMin), fillColor);
        }
    }

    private void GenerateHeatmapTexture()
    {
        if (visualizationData?.atlasTexture == null || visualizationData.triangleData == null)
            return;

        int width = visualizationData.atlasTexture.width;
        int height = visualizationData.atlasTexture.height;

        heatmapTexture = new Texture2D(width, height, TextureFormat.RGB24, false);

        // Create density arrays for each pixel
        float[] pixelMinDensity = new float[width * height];
        float[] pixelMaxDensity = new float[width * height];
        float[] pixelMeanDensity = new float[width * height];
        int[] pixelTriangleCount = new int[width * height];

        // Initialize arrays
        for (int i = 0; i < pixelMinDensity.Length; i++)
        {
            pixelMinDensity[i] = float.MaxValue;
            pixelMaxDensity[i] = 0f;
            pixelMeanDensity[i] = 0f;
            pixelTriangleCount[i] = 0;
        }

        // Rasterize each triangle to calculate per-pixel density
        foreach (var triangle in visualizationData.triangleData)
        {
            if (triangle.texelsPerMeter <= 0) continue;

            RasterizeTriangleForHeatmap(triangle, width, height, pixelMinDensity, pixelMaxDensity, pixelMeanDensity, pixelTriangleCount);
        }

        // Finalize mean calculations and find global min/max for normalization
        float globalMin = float.MaxValue;
        float globalMax = 0f;

        for (int i = 0; i < pixelMeanDensity.Length; i++)
        {
            if (pixelTriangleCount[i] > 0)
            {
                pixelMeanDensity[i] /= pixelTriangleCount[i];

                if (pixelMinDensity[i] == float.MaxValue) pixelMinDensity[i] = 0f;

                float density = heatmapDisplayMode switch
                {
                    0 => pixelMaxDensity[i],
                    1 => pixelMinDensity[i],
                    2 => pixelMeanDensity[i],
                    _ => pixelMaxDensity[i]
                };

                if (density > 0)
                {
                    globalMin = Mathf.Min(globalMin, density);
                    globalMax = Mathf.Max(globalMax, density);
                }
            }
        }

        if (globalMin == float.MaxValue) globalMin = 0f;

        // Generate heatmap colors
        Color[] colors = new Color[width * height];
        for (int y = 0; y < height; y++)
        {
            for (int x = 0; x < width; x++)
            {
                int index = y * width + x;

                float density = heatmapDisplayMode switch
                {
                    0 => pixelMaxDensity[index],
                    1 => pixelMinDensity[index] == float.MaxValue ? 0f : pixelMinDensity[index],
                    2 => pixelMeanDensity[index],
                    _ => pixelMaxDensity[index]
                };

                if (density <= 0 || pixelTriangleCount[index] == 0)
                {
                    colors[index] = Color.black;
                }
                else
                {
                    float normalized = globalMax > globalMin ? (density - globalMin) / (globalMax - globalMin) : 0f;
                    colors[index] = GetHeatmapColor(normalized);
                }
            }
        }

        heatmapTexture.SetPixels(colors);
        heatmapTexture.Apply();
    }

    private void RasterizeTriangleForHeatmap(MeshTriangleData triangle, int width, int height,
        float[] pixelMinDensity, float[] pixelMaxDensity, float[] pixelMeanDensity, int[] pixelTriangleCount)
    {
        // Convert UV coordinates to pixel coordinates
        Vector2 p0 = new Vector2(triangle.uv0.x * width, triangle.uv0.y * height);
        Vector2 p1 = new Vector2(triangle.uv1.x * width, triangle.uv1.y * height);
        Vector2 p2 = new Vector2(triangle.uv2.x * width, triangle.uv2.y * height);

        // Get bounding box
        int minX = Mathf.Max(0, Mathf.FloorToInt(Mathf.Min(p0.x, Mathf.Min(p1.x, p2.x))));
        int maxX = Mathf.Min(width - 1, Mathf.CeilToInt(Mathf.Max(p0.x, Mathf.Max(p1.x, p2.x))));
        int minY = Mathf.Max(0, Mathf.FloorToInt(Mathf.Min(p0.y, Mathf.Min(p1.y, p2.y))));
        int maxY = Mathf.Min(height - 1, Mathf.CeilToInt(Mathf.Max(p0.y, Mathf.Max(p1.y, p2.y))));

        // Rasterize triangle
        for (int y = minY; y <= maxY; y++)
        {
            for (int x = minX; x <= maxX; x++)
            {
                Vector2 pixelCenter = new Vector2(x + 0.5f, y + 0.5f);

                if (IsPointInTriangle(pixelCenter, p0, p1, p2))
                {
                    int index = y * width + x;

                    pixelMinDensity[index] = Mathf.Min(pixelMinDensity[index], triangle.texelsPerMeter);
                    pixelMaxDensity[index] = Mathf.Max(pixelMaxDensity[index], triangle.texelsPerMeter);
                    pixelMeanDensity[index] += triangle.texelsPerMeter;
                    pixelTriangleCount[index]++;
                }
            }
        }
    }

    private Color GetHeatmapColor(float value)
    {
        // Blue to Red heatmap
        value = Mathf.Clamp01(value);

        if (value < 0.25f)
            return Color.Lerp(Color.blue, Color.cyan, value * 4f);
        else if (value < 0.5f)
            return Color.Lerp(Color.cyan, Color.green, (value - 0.25f) * 4f);
        else if (value < 0.75f)
            return Color.Lerp(Color.green, Color.yellow, (value - 0.5f) * 4f);
        else
            return Color.Lerp(Color.yellow, Color.red, (value - 0.75f) * 4f);
    }

    private void HandleHeatmapClick(Vector2 mousePosition, Rect displayRect)
    {
        if (visualizationData?.atlasTexture == null)
            return;

        // Convert mouse position to atlas pixel coordinates
        Vector2 relativePos = (mousePosition - displayRect.position) / displayRect.size;
        selectedPixelCoord = new Vector2(
            relativePos.x * visualizationData.atlasTexture.width,
            (1f - relativePos.y) * visualizationData.atlasTexture.height // Flip Y
        );

        hasSelectedPixel = true;
        Repaint();
    }

    private void DrawHeatmapInfo()
    {
        EditorGUILayout.LabelField("Heatmap Information", EditorStyles.centeredGreyMiniLabel);

        if (!hasSelectedPixel)
        {
            EditorGUILayout.HelpBox("Click on the heatmap to see texel density information for that pixel.", MessageType.Info);
            return;
        }

        if (visualizationData?.triangleData == null)
        {
            EditorGUILayout.HelpBox("No triangle data available.", MessageType.Warning);
            return;
        }

        int x = Mathf.FloorToInt(selectedPixelCoord.x);
        int y = Mathf.FloorToInt(selectedPixelCoord.y);
        int width = visualizationData.atlasTexture.width;
        int height = visualizationData.atlasTexture.height;

        if (x < 0 || x >= width || y < 0 || y >= height)
            return;

        // Find all triangles that cover this pixel
        Vector2 pixelUV = new Vector2((x + 0.5f) / width, (y + 0.5f) / height);
        var coveringTriangles = new List<MeshTriangleData>();

        foreach (var triangle in visualizationData.triangleData)
        {
            if (IsPointInTriangle(pixelUV, triangle.uv0, triangle.uv1, triangle.uv2))
            {
                coveringTriangles.Add(triangle);
            }
        }

        EditorGUILayout.LabelField($"Pixel: ({x}, {y})", EditorStyles.boldLabel);
        EditorGUILayout.Space();

        if (coveringTriangles.Count == 0)
        {
            EditorGUILayout.LabelField("No triangles cover this pixel.");
            return;
        }

        // Calculate min/max/mean from covering triangles
        float minDensity = float.MaxValue;
        float maxDensity = 0f;
        float totalDensity = 0f;

        foreach (var triangle in coveringTriangles)
        {
            if (triangle.texelsPerMeter > 0)
            {
                minDensity = Mathf.Min(minDensity, triangle.texelsPerMeter);
                maxDensity = Mathf.Max(maxDensity, triangle.texelsPerMeter);
                totalDensity += triangle.texelsPerMeter;
            }
        }

        if (minDensity == float.MaxValue) minDensity = 0f;
        float meanDensity = coveringTriangles.Count > 0 ? totalDensity / coveringTriangles.Count : 0f;

        EditorGUILayout.LabelField("Texel Density (texels/meter):");
        EditorGUILayout.LabelField($"  Max: {maxDensity:F2}");
        EditorGUILayout.LabelField($"  Min: {minDensity:F2}");
        EditorGUILayout.LabelField($"  Mean: {meanDensity:F2}");

        EditorGUILayout.Space();

        // Group triangles by mesh
        var meshGroups = coveringTriangles.GroupBy(t => t.meshInstanceId).ToList();

        EditorGUILayout.LabelField($"Meshes using this pixel ({meshGroups.Count}):");
        foreach (var group in meshGroups)
        {
            var firstTriangle = group.First();
            var triangleCount = group.Count();
            var avgDensity = group.Average(t => t.texelsPerMeter);

            EditorGUILayout.LabelField($"  {firstTriangle.meshName} ({triangleCount} triangles): {avgDensity:F2} texels/m");
        }

        // Show source texture info if available
        EditorGUILayout.Space();
        var mapping = GetMappingForPixel(x, y);
        if (mapping != null)
        {
            EditorGUILayout.LabelField("Source Texture:");
            EditorGUILayout.LabelField(mapping.originalTexturePath, EditorStyles.wordWrappedMiniLabel);
        }
    }

    private AtlasRectMapping GetMappingForPixel(int x, int y)
    {
        if (visualizationData?.mappings == null)
            return null;

        foreach (var mapping in visualizationData.mappings)
        {
            if (mapping.atlasPixelRect.Contains(new Vector2(x, y)))
                return mapping;
        }

        return null;
    }

    private bool IsPointInTriangle(Vector2 point, Vector2 v0, Vector2 v1, Vector2 v2)
    {
        // Barycentric coordinate method
        var denom = (v1.y - v2.y) * (v0.x - v2.x) + (v2.x - v1.x) * (v0.y - v2.y);
        if (Mathf.Abs(denom) < 0.0001f) return false;

        var a = ((v1.y - v2.y) * (point.x - v2.x) + (v2.x - v1.x) * (point.y - v2.y)) / denom;
        var b = ((v2.y - v0.y) * (point.x - v2.x) + (v0.x - v2.x) * (point.y - v2.y)) / denom;
        var c = 1 - a - b;

        return a >= -0.001f && b >= -0.001f && c >= -0.001f; // Small epsilon for edge cases
    }
}