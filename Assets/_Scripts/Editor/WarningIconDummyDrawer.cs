using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

[InitializeOnLoad]
public static class CustomPrefabIconDrawer
{
    private static Texture2D customIcon;
    
    private static List<PrefabOptimiseController.Configuration> _configs;

    static CustomPrefabIconDrawer()
    {
        customIcon = AssetDatabase.LoadAssetAtPath<Texture2D>("Assets/_Art/GUI/Editor/Warning.png");

        if (customIcon != null)
        {
            EditorApplication.projectWindowItemOnGUI += OnProjectWindowGUI;
            EditorApplication.update += () => _configs = PrefabOptimiseController.GetConfigs();
        }
    }

    private static void OnProjectWindowGUI(string guid, Rect selectionRect)
    {
        if (customIcon == null) return;
        
        string path = AssetDatabase.GUIDToAssetPath(guid).Replace("\\", "/");
        if (!path.EndsWith(".prefab") && path.Contains('.') || path.Contains("Editor")) return;
        foreach (var config in _configs)
        {
            if (!path.StartsWith(config.directoryTo)) continue;
        
            var iconRect = new Rect(selectionRect.x, selectionRect.y, selectionRect.height, selectionRect.height);
            GUI.DrawTexture(iconRect, customIcon, ScaleMode.ScaleToFit);
            return;
        }
    }
}