using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class ProductTestingManager : SubSceneManager<ProductTestingManager>
{
    override public string SceneName => "ProductTesting";
    
    public GameObject m_emotePrefab;
    public NGOrderTile m_orderTile;
    private MAOrder m_order;
    private bool m_isSkipped = false;
    
    public enum EProductTestMode
    {
        Auto = -1,
        Eat,
        Attack,
        Defend,
        Model,
    }
    public enum EProductTestGender
    {
        Male,
        Female,
    }

    public class RewardTableEntry
    {
        public enum EEmote
        {
            Sad,
            Ok,
            Happy,
            Joy,
        };
        public EEmote[] m_emote;
        public EProductTestResult m_result;
        
        public RewardTableEntry(EProductTestResult _emote, EEmote[] _emot)
        {
            m_result = _emote;
            m_emote = _emot;
        }
    }
    
    // Must be 0 based, and ordered from least to most best
    public enum EProductTestResult
    {
        Sad,
        Unhappy,
        Disappointed,
        Surprised,
        Delighted,
        Sublime,
        Orgasmic,
    }
    
    static RewardTableEntry[] c_rewardTable =
    {
        new RewardTableEntry(EProductTestResult.Sad, new[] {RewardTableEntry.EEmote.Sad, RewardTableEntry.EEmote.Sad, RewardTableEntry.EEmote.Sad, RewardTableEntry.EEmote.Sad, RewardTableEntry.EEmote.Sad, RewardTableEntry.EEmote.Sad, RewardTableEntry.EEmote.Sad}),
        new RewardTableEntry(EProductTestResult.Unhappy, new[] {RewardTableEntry.EEmote.Sad, RewardTableEntry.EEmote.Sad, RewardTableEntry.EEmote.Sad, RewardTableEntry.EEmote.Sad }),
        new RewardTableEntry(EProductTestResult.Disappointed, new[] {RewardTableEntry.EEmote.Ok, RewardTableEntry.EEmote.Ok, RewardTableEntry.EEmote.Ok, RewardTableEntry.EEmote.Ok}),
        new RewardTableEntry(EProductTestResult.Surprised, new[] {RewardTableEntry.EEmote.Ok, RewardTableEntry.EEmote.Ok, RewardTableEntry.EEmote.Happy, RewardTableEntry.EEmote.Happy }),
        new RewardTableEntry(EProductTestResult.Delighted, new[] {RewardTableEntry.EEmote.Happy, RewardTableEntry.EEmote.Happy, RewardTableEntry.EEmote.Happy, RewardTableEntry.EEmote.Happy}),
        new RewardTableEntry(EProductTestResult.Sublime, new[] {RewardTableEntry.EEmote.Joy, RewardTableEntry.EEmote.Joy, RewardTableEntry.EEmote.Joy, RewardTableEntry.EEmote.Joy}),
        new RewardTableEntry(EProductTestResult.Orgasmic, new[] {RewardTableEntry.EEmote.Joy, RewardTableEntry.EEmote.Joy, RewardTableEntry.EEmote.Joy, RewardTableEntry.EEmote.Joy, RewardTableEntry.EEmote.Joy, RewardTableEntry.EEmote.Joy, RewardTableEntry.EEmote.Joy}),
    };    

    public EProductTestMode m_mode = EProductTestMode.Attack;
    public EProductTestGender m_gender = EProductTestGender.Male;
    public string m_design;

    public GameObject m_character1;
    public GameObject m_character2;
    public GameObject m_character1Female;
    public GameObject m_character2Female;
    public AnimationHandler m_character1Handler;
    public AnimationHandler m_character2Handler;
    private AnimationOverride m_character1Override;
    private AnimationOverride m_character2Override;
    public Transform m_preAnimationAttachTransform;
    public Shader m_characterShader;
    
    public GameObject m_chr1VisualsNormal;
    public GameObject m_chr1VisualsNaked;
    
    private GameObject m_character1Used, m_character2Used;

    [System.Serializable]
    public class TestEffect
    {
        public GameObject m_prefab;
        public float m_delay;
        public Vector3 m_positionOffset;
        public Vector3 m_rotationOffset;

        public void Trigger(Transform _owner)
        {
            if (m_prefab == null) return;
            Utility.After(m_delay, () =>
            {
                var go = GameObject.Instantiate(m_prefab, _owner);
                go.transform.localPosition = m_positionOffset;
                go.transform.localEulerAngles = m_rotationOffset;
            });
        }
    }
    
    [System.Serializable]
    public class TestSetup
    {
        public string m_testName;
        public bool m_swapCharacters = false;
        public float m_attachObjectScale = 1.0f;
        public string m_loopChr1Anim;
        public string m_loopChr2Anim;
        public float m_attachChr1Time;
        public string m_testChr1Anim;
        public string m_testChr1BadAnim;
        public string m_testChr1NeutralAnim;
        public string m_testChr1GoodAnim;
        public string m_testChr1BadPostAnim;
        public string m_testChr1NeutralPostAnim;
        public string m_testChr1GoodPostAnim;
        public float m_testChr1Delay = 0;
        public string m_testChr2Anim;
        public string m_testChr2BadAnim;
        public string m_testChr2NeutralAnim;
        public string m_testChr2GoodAnim;
        public string m_testChr2BadPostAnim;
        public string m_testChr2NeutralPostAnim;
        public string m_testChr2GoodPostAnim;
        public float m_testChr2Delay = 0;
        public string m_mainAudio;
        public string m_secondaryAudio;
        public string m_responseAudioGood;
        public string m_responseAudioNeutral;
        public string m_responseAudioBad;
        public Vector3 m_chr1AttachPos;
        public Vector3 m_chr1AttachRot;
        public Vector3 m_chr2AttachPos;
        public Vector3 m_chr2AttachRot;
        public Transform m_chr1Start;
        public Transform m_chr2Start;
        public TestEffect m_chr1AnimEffectBad;
        public TestEffect m_chr1AnimEffectNeutral;
        public TestEffect m_chr1AnimEffectGood;
        public TestEffect m_chr2AnimEffectBad;
        public TestEffect m_chr2AnimEffectNeutral;
        public TestEffect m_chr2AnimEffectGood;
    }
    public TestSetup[] m_tests;
    private TestSetup m_current;
    
    private bool UseChr2 => m_current.m_chr2Start != null;
    
    private int m_waitingForComplete;
    
    private RagdollController m_receiverRC = null;
    
    private Transform m_chr1RightHand, m_chr1Body;
    private Transform m_chr2RightHand, m_chr2Body;
    
    private GameObject m_chr1HeldObjects, m_chr2HeldObjects;
    
    private UnityEngine.UI.Button m_skipButton;

    private RewardTableEntry m_rewardEntry;

    public void Activate(EProductTestMode _mode, MAOrder _order, EProductTestResult _result, string _design)
    {
        KeyboardShortcutManager.Me.PushShortcuts(KeyboardShortcutManager.EShortcutType.ProductTesting);

        m_order = _order;
        m_rewardEntry = c_rewardTable[Mathf.Clamp((int)_result, 0, c_rewardTable.Length)];
        
        if (_mode == EProductTestMode.Auto)
            _mode = GetModeForOrder(_order);
        m_gender = GetGenderForOrder(_order);
        m_mode = _mode;
        m_design = _design;
        m_isSkipped = false;
        Activate();
    }

    public static EProductTestMode GetModeForOrder(MAOrder _order)
    {
        switch (_order.ProductLine)
        {
            case "WorkerFood":
            case "Food":
                return EProductTestMode.Eat;
            case "Weapons":
                return EProductTestMode.Attack;
            case DesignTableManager.c_armourMale:
            case DesignTableManager.c_armourFemale:
                return EProductTestMode.Defend;
            case DesignTableManager.c_clothingMale:
            case DesignTableManager.c_clothingFemale:
                return EProductTestMode.Model;
        }
        return EProductTestMode.Auto;
    }

    public static EProductTestGender GetGenderForOrder(MAOrder _order)
    {
        switch (_order.ProductLine)
        {
            case DesignTableManager.c_armourFemale:
            case DesignTableManager.c_clothingFemale:
                return EProductTestGender.Female;
        }
        return EProductTestGender.Male;
    }
    
    public string m_defaultAttackerWeaponDesign = "4|Weapon_Pummel_A@|Weapon_Handle_A@|Weapon_Hilt_A@|Weapon_Blade_BrowdSword_A@|3|1.1.0.0.0|2.1.1.0.0|3.1.2.0.0|";
    public string m_defaultDefenderArmourDesign = "0|0|";//5|Clothing_Jackets_Body_Shakespeare@|Clothing_Jackets_Sleeve_Shakespeare@|Clothing_Jackets_Sleeve_Shakespeare@|Clothing_Jackets_Collar_Ruffle@|Dresses_Wings_Butterfly@|4|1.0.0.3.0|2.0.0.4.0|3.0.0.1.0|4.0.0.7.0|";

    float HeldItemScale => GlobalData.Me.m_heldItemScale * m_current.m_attachObjectScale;
    
    private const bool c_showEndButtonsImmediately = true;
    
    private List<GameObject> m_addedObjects = new();
    
    private int m_activateCooldown = 0;
    override public bool ActivateComplete()
    {
        if (true)
        {
            if (m_activateCooldown == 0)
            {
                var heroAH = m_character2.GetComponentInChildren<AnimationHandler>();
                var workerAH = m_character1.GetComponentInChildren<AnimationHandler>();
                AnimationOverride.PlayClip(heroAH.gameObject, "HeroShout", null);
                AnimationOverride.PlayClip(workerAH.gameObject, "WorkerWave", null);
                m_activateCooldown = 4;
                return false;
            }
            --m_activateCooldown;
            if (m_activateCooldown > 0)
                return false;
        }
        
        base.ActivateComplete();
        transform.position = new Vector3(500, 0, 0);
        
        foreach (var list in m_subAnims) list.Clear();
        
        GameManager.Me.m_subSceneExitButton?.SetActive(false);
        
        m_current = m_tests[(int)m_mode];
        
        m_chr1VisualsNormal.SetActive(m_mode != EProductTestMode.Model);
        m_chr1VisualsNaked.SetActive(m_mode == EProductTestMode.Model);

        m_orderTile.SetupOrderUI(m_order);
        m_orderTile.LockRoot.gameObject.SetActive(false);

        if (m_current.m_swapCharacters)
        {
            m_character1Used = m_gender == EProductTestGender.Female ? m_character2Female : m_character2;
            m_character2Used = m_character1;
        }
        else
        {
            m_character1Used = m_gender == EProductTestGender.Female ? m_character1Female : m_character1;
            m_character2Used = m_character2;
        }
        m_character1.SetActive(m_character1Used == m_character1);
        m_character1Female.SetActive(m_character1Used == m_character1Female);
        m_character2.SetActive(m_character2Used == m_character2);
        m_character2Female.SetActive(m_character2Used == m_character2Female);
        
        m_character1Handler = m_character1Used.GetComponentInChildren<AnimationHandler>();
        if (m_character1Handler == null) m_character1Handler = m_character1Used.AddComponent<AnimationHandler>();
        m_character2Handler = m_character2Used.GetComponentInChildren<AnimationHandler>();
        if (m_character2Handler == null) m_character2Handler = m_character2Used.AddComponent<AnimationHandler>();
        SetCharacterDetails(m_character1Used);
        SetCharacterDetails(m_character2Used);
        
        var ah1 = m_character1Handler;
        var ah2 = m_character2Handler;
        
        m_chr1RightHand = ah1.GetAttachBone(AttachmentBone.RIGHT_HAND);
        if (m_chr1RightHand != null)
        {
            m_chr1RightHand.localPosition = m_current.m_chr1AttachPos;
            m_chr1RightHand.localEulerAngles = m_current.m_chr1AttachRot;
        }
        m_chr2RightHand = ah2.GetAttachBone(AttachmentBone.RIGHT_HAND);
        if (m_chr2RightHand != null)
        {
            m_chr2RightHand.localPosition = m_current.m_chr2AttachPos;
            m_chr2RightHand.localEulerAngles = m_current.m_chr2AttachRot;
        }
        m_chr1Body = ah1.GetAttachBone(AttachmentBone.BODY);
        m_chr2Body = ah2.GetAttachBone(AttachmentBone.BODY);
        var receiver = m_character2Used;
        if (m_mode == EProductTestMode.Eat)
        {
            var attachTo = m_chr1RightHand;
            if (m_current.m_attachChr1Time > 0) attachTo = m_preAnimationAttachTransform;
            m_chr1HeldObjects = ah1.AddHeldObject(attachTo, m_design, Vector3.zero, Vector3.zero, HeldItemScale);
            var plate = Instantiate(DesignTableManager.Me.m_platePrefab, transform);
            plate.transform.localScale = Vector3.one * .15f;
            plate.transform.localPosition = Vector3.up * 1.5f;
            m_addedObjects.Add(plate);
        }
        else if (m_mode == EProductTestMode.Attack)
        {
            m_chr1HeldObjects = ah1.AddHeldObject(m_chr1RightHand, m_design, HoldPoint.DefaultHoldOffset, new Vector3(0, 0, 0), HeldItemScale, _o => MeasureAndMoveCharacters(_o, ah1), null, true);
            ah2.AddClothing(m_defaultDefenderArmourDesign, HeldItemScale);
        }
        else if (m_mode == EProductTestMode.Defend)
        {
            receiver = m_character1Used;
            ah1.AddClothing(m_design, HeldItemScale);
            m_chr2HeldObjects = ah2.AddHeldObject(m_chr2RightHand, m_defaultAttackerWeaponDesign, HoldPoint.DefaultHoldOffset, new Vector3(0, 0, 0), HeldItemScale, _o => MeasureAndMoveCharacters(_o, ah2), null, true);
        }
        else if (m_mode == EProductTestMode.Model)
        {
            ah1.AddClothing(m_design, HeldItemScale);
        }

        GetComponent<EnableByName>().Enable(m_mode.ToString());

        m_character1Used.SetActive(true);
        m_character2Used.SetActive(UseChr2);
        
        m_character1Used.transform.position = m_current.m_chr1Start.position;
        m_character1Used.transform.rotation = m_current.m_chr1Start.rotation;
        if (UseChr2)
        {
            m_character2Used.transform.position = m_current.m_chr2Start.position;
            m_character2Used.transform.rotation = m_current.m_chr2Start.rotation;

            m_receiverRC = receiver.GetComponentInChildren<RagdollController>();
            m_receiverRC.StartAnimatedState();
            //Utility.After(0.2f, () => m_receiverRC.StartResponsiveState());
        }
        
        PlayIdle();
        
        m_waitingForComplete = UseChr2 ? 2 : 1;

        m_skipButton = UIManager.Me.transform.Find("ProductTestUI/Test").GetComponent<UnityEngine.UI.Button>();
        m_skipButton.gameObject.SetActive(true);
        m_skipButton.interactable = true;
        m_skipButton.GetComponentInChildren<TMPro.TextMeshProUGUI>().text = "Skip ";
        m_skipButton.onClick.RemoveAllListeners();
        m_skipButton.onClick.AddListener(() => SkipTest());
        
        if (c_showEndButtonsImmediately)
            ShowEndButtons();
        
        StartCoroutineOnRoot(Co_TriggerTest());
        
        return true;
    }

    void SkipTest()
    {
        if (m_isSkipped) return;
        m_skipButton.interactable = false;
        m_isSkipped = true;
        AnimationOverride.Stop(m_character1Handler.gameObject, true);
        if (UseChr2)
            AnimationOverride.Stop(m_character2Handler.gameObject, true);
        while (m_waitingForComplete > 0)
            EndTest();
    }

    IEnumerator Co_WaitFor(float _time)
    {
        while (_time > 0)
        {
            yield return null;
            _time -= Time.deltaTime;
        }
    }

    IEnumerator Co_TriggerTest()
    {
        yield return Co_WaitFor(1);//new WaitForSeconds(1);
        TriggerTest();
    }

    void PlayIdle()
    {
        m_character1Override = AnimationOverride.PlayClip(m_character1Handler.gameObject, m_current.m_loopChr1Anim, _ => { });
        PlaySubAnims(m_current.m_loopChr1Anim, true);
        StartCoroutineOnRoot(Co_PauseLoco(m_character1Handler.gameObject));
        if (UseChr2)
        {
            m_character2Override = AnimationOverride.PlayClip(m_character2Handler.gameObject, m_current.m_loopChr2Anim, _ => { });
            PlaySubAnims(m_current.m_loopChr2Anim, false);
            StartCoroutineOnRoot(Co_PauseLoco(m_character2Handler.gameObject));
        }
    }

    IEnumerator Co_PauseLoco(GameObject _chr)
    {
        yield return Co_WaitFor(.2f);//new WaitForSeconds(.2f);
        _chr.GetComponent<Animator>().SetBool("PauseLoco", true);
    }

    void RemoveAllHeldObjects()
    {
        m_character1Handler.gameObject.GetComponent<AnimationHandler>().ClearClothing();
        m_character2Handler.gameObject.GetComponent<AnimationHandler>().ClearClothing();
        if (m_chr1HeldObjects != null)
        {
            Destroy(m_chr1HeldObjects);
            m_chr1HeldObjects = null;
        }
        if (m_chr2HeldObjects != null)
        {
            Destroy(m_chr2HeldObjects);
            m_chr2HeldObjects = null;
        }
    }
    
    void FixClothing(GameObject _o, bool _isChr1)
    {
        foreach (var blk in _o.GetComponentsInChildren<Block>())
        {
            var container = blk.m_toVisuals;
            var charRoot = container.FindRecursiveByName<Transform>("CharacterRoot");
            if (charRoot == null) continue;
            charRoot.parent.gameObject.AddComponent<Animator>();
            var ah = charRoot.parent.gameObject.AddComponent<AnimationHandler>();
            AddSubAnimator(ah, _isChr1);
        }
        PlaySubAnims(_isChr1 ? m_current.m_loopChr1Anim : m_current.m_loopChr2Anim, _isChr1);
    }

    private List<AnimationHandler>[] m_subAnims = { new List<AnimationHandler>(), new List<AnimationHandler>() };
    void AddSubAnimator(AnimationHandler _handler, bool _isChr1)
    {
        int index = _isChr1 ? 0 : 1;
        m_subAnims[index].Add(_handler);
    }

    void PlaySubAnims(string _anim, bool _isChr1)
    {
        int index = _isChr1 ? 0 : 1;
        foreach (var ah in m_subAnims[index]) AnimationOverride.PlayClip(ah.gameObject, _anim, _b => { });
    }

    void MeasureAndMoveCharacters(GameObject _o, AnimationHandler _holder)
    {
        var trans = _o.transform;
        var backupRot = trans.rotation;
        trans.rotation = Quaternion.identity;
        var bounds = ManagedBlock.GetTotalVisualBounds(_o);
        trans.rotation = backupRot;
        var finalScale = _holder.transform.lossyScale.x * HeldItemScale;
        var height = bounds.size.y * finalScale;
        var pos1 = m_character1Used.transform.position;
        var pos2 = m_character2Used.transform.position;
        var p1to2 = pos2 - pos1;
        const float c_baseHeightDifference = 1.25f;
        var requiredDiff = p1to2.normalized * (height + c_baseHeightDifference);
        var offsetEach = (p1to2 - requiredDiff) * .5f;
        m_character1Used.transform.position += offsetEach;
        m_character2Used.transform.position -= offsetEach;
    }


    void SetCharacterDetails(GameObject _chr)
    {
        return;
        
        var clothes = Color.HSVToRGB(Random.Range(0.0f, 1.0f), .5f, .8f);
        clothes = GlobalData.Me.PrepareClothingColour(clothes);
        var skin = GlobalData.Me.GetSkinColour(Random.Range(0.0f, 1.0f));
        var hair = GlobalData.Me.GetHairColour(Random.Range(0.0f, 1.0f));
        Extensions.SetTintWindowColour(_chr, 0, clothes, true);
        Extensions.SetTintWindowColour(_chr, 1, skin, true);
        Extensions.SetTintWindowColour(_chr, 2, hair, true);
    }

    string ResponseAnim1 => Choose(m_current.m_testChr1BadAnim, m_current.m_testChr1NeutralAnim, m_current.m_testChr1GoodAnim);
    string ResponseAnim2 => Choose(m_current.m_testChr2BadAnim, m_current.m_testChr2NeutralAnim, m_current.m_testChr2GoodAnim);
    string ResponseAudio => Choose(m_current.m_responseAudioBad, m_current.m_responseAudioNeutral, m_current.m_responseAudioGood);
    string PostIdleAnim1 => Choose(m_current.m_testChr1BadPostAnim, m_current.m_testChr1NeutralPostAnim, m_current.m_testChr1GoodPostAnim);
    string PostIdleAnim2 => Choose(m_current.m_testChr2BadPostAnim, m_current.m_testChr2NeutralPostAnim, m_current.m_testChr2GoodPostAnim);
    TestEffect ResponseEffect1 => Choose(m_current.m_chr1AnimEffectBad, m_current.m_chr1AnimEffectNeutral, m_current.m_chr1AnimEffectGood);
    TestEffect ResponseEffect2 => Choose(m_current.m_chr2AnimEffectBad, m_current.m_chr2AnimEffectNeutral, m_current.m_chr2AnimEffectGood);

    void TriggerTest()
    {
        if (m_current.m_mainAudio != null)
            AudioClipManager.Me.PlaySound(m_current.m_mainAudio, m_character1Used);
        StartCoroutineOnRoot(Co_TriggerAnimation(m_character1Handler.gameObject, m_current.m_testChr1Anim, ResponseAnim1, ResponseAudio, PostIdleAnim1, ResponseEffect1, m_current.m_testChr1Delay, true));
        if (UseChr2)
        {
            StartCoroutineOnRoot(Co_TriggerAnimation(m_character2Handler.gameObject, m_current.m_testChr2Anim, ResponseAnim2, null, PostIdleAnim2, ResponseEffect2, m_current.m_testChr2Delay, false));
            if (m_current.m_secondaryAudio != null)
                AudioClipManager.Me.PlaySound(m_current.m_secondaryAudio, m_character2Used);
        }
        if (m_current.m_attachChr1Time > 0)
            StartCoroutineOnRoot(Co_LateAttach());
    }

    IEnumerator Co_LateAttach()
    {
        if(m_chr1HeldObjects == null) yield break;
        yield return Co_WaitFor(m_current.m_attachChr1Time);//new WaitForSeconds(m_current.m_attachChr1Time);
        if(m_chr1HeldObjects == null) yield break;
        var trans = m_chr1HeldObjects.transform;
        trans.SetParent(m_chr1RightHand, true);
        trans.localScale = Vector3.one * (HeldItemScale / trans.parent.lossyScale.x);
        trans.localPosition = Vector3.zero;
        trans.localEulerAngles = Vector3.zero;
    }
    
    private T Choose<T>(T _bad, T _neutral, T _good)
    {
        switch (m_rewardEntry.m_result)
        {
            default:
            case EProductTestResult.Sad:
            case EProductTestResult.Unhappy:
            case EProductTestResult.Disappointed:
                return _bad;
            
            case EProductTestResult.Surprised:
            case EProductTestResult.Delighted: 
                return _neutral;
            
            case EProductTestResult.Sublime:
            case EProductTestResult.Orgasmic: 
                return _good;
        }
    }

    IEnumerator Co_TriggerAnimation(GameObject _o, string _anim, string _resultAnim, string _resultAudio, string _finalIdle, TestEffect _effect, float _delay, bool _isChr1)
    {
        yield return Co_WaitFor(_delay);//new WaitForSeconds(_delay);
        if (string.IsNullOrEmpty(_anim))
        {
            _anim = _resultAnim;
            _resultAnim = null;
            if (_effect != null)
            {
                _effect.Trigger(_o.transform);
                _effect = null;
            }
        }
        if (string.IsNullOrEmpty(_finalIdle) == false)
            this.DoAfter(.25f, () => (_isChr1 ? m_character1Override : m_character2Override).InsertClip(_finalIdle, null, true));
        AnimationOverride.PlayClip(_o, _anim, _ => {
            if (m_mode == EProductTestMode.Eat) RemoveAllHeldObjects();
            if (string.IsNullOrEmpty(_resultAudio) == false)
                AudioClipManager.Me.PlaySound(_resultAudio, _o);
            if (_effect != null) _effect.Trigger(_o.transform);
            if (string.IsNullOrEmpty(_resultAnim))
            {
                EndTest();
            }
            else
            {
                AnimationOverride.PlayClip(_o, _resultAnim, _ => EndTest(), _doNotInterruptPrevious : true);
                PlaySubAnims(_resultAnim, _isChr1);
            }
        }, _doNotInterruptPrevious : true);
        PlaySubAnims(_anim, _isChr1);
        StartCoroutineOnRoot(Co_ShowRewards());
    }

    IEnumerator Co_ShowRewards()
    {
        float t = 0;
        const float c_totalRewardsLength = 8;
        const float c_totalRewardSteps = 4;
        float currentReward = 0;
        float tToNextEmote = 0.15f; 
        int nextEmote = 0;
        m_orderTile.SetupOrderUI(m_order);
        while (t <= 1)
        {
            float deltaMultiply = m_isSkipped ? 6 : 1;
            t += Time.deltaTime * deltaMultiply / c_totalRewardsLength;
            var tReward = Mathf.Min(1, t * 1.4f);
            float nextReward = ((int) (tReward * c_totalRewardSteps)) / c_totalRewardSteps;
            currentReward = Mathf.Lerp(currentReward, nextReward, .15f);
            // TODO
            //m_orderTile.UpdateProgress(currentReward < 1.0f / c_totalRewardSteps ? -1 : fractionGained, currentReward);
            
            if (t >= tToNextEmote && nextEmote < m_rewardEntry.m_emote.Length)
            {
                tToNextEmote += .1f;
                var emote = GameObject.Instantiate(m_emotePrefab);
                var emoteType = m_rewardEntry.m_emote[nextEmote++];
                emote.GetComponent<SetSpriteByName>().SetSprite(emoteType.ToString());
                var hat = m_character1Handler.gameObject.GetComponent<AnimationHandler>().GetAttachBone(AttachmentBone.HAT);
                if (hat == null)
                {
                    Debug.LogError($"Error: ProductTestingManager didn't find hat bone");
                    hat = m_character1Used.transform;
                }
                var spawnPos = hat.transform.position;
                emote.transform.position = spawnPos;
                emote.transform.forward = Camera.main.transform.position - emote.transform.position;
                
                string emoteAudio;
                switch (emoteType)
                {
                    default:
                    case RewardTableEntry.EEmote.Sad:
                        emoteAudio = "PlaySound_TestingRoomEmojiBad";
                        break;
                    case RewardTableEntry.EEmote.Ok:
                        emoteAudio = "PlaySound_TestingRoomEmojiOK";
                        break;
                    case RewardTableEntry.EEmote.Happy:
                    case RewardTableEntry.EEmote.Joy:
                        emoteAudio = "PlaySound_TestingRoomEmojiGood";
                        break;
                }
                AudioClipManager.Me.PlaySound(emoteAudio, GameManager.Me.gameObject);
            }
            yield return null;
        }
    }

    void EndTest()
    {
        if (c_showEndButtonsImmediately) return;
        --m_waitingForComplete;
        if (m_waitingForComplete == 0)
        {
            ShowEndButtons();
        }
    }

    private ProductTestUIController m_dialog;
    void ShowEndButtons()
    {
        m_skipButton.gameObject.SetActive(false);
        var dialog = InfoPlaqueManager.Me.LoadUI<ProductTestUIController>(true);
        dialog.ToggleSwitchables(m_rewardEntry.m_result.ToString());
        var retryButton = dialog.transform.FindRecursiveByName<UnityEngine.UI.Button>("ButtonRetry");
        var acceptButton = dialog.transform.FindRecursiveByName<UnityEngine.UI.Button>("ButtonAccept");
        retryButton.onClick.RemoveAllListeners();
        retryButton.onClick.AddListener(() =>
        {
            dialog.Close();
            AudioClipManager.Me.PlayUISound("PlaySound_ProductTestingRoomRedesign");
            Leave(false);
        });
        acceptButton.onClick.RemoveAllListeners();
        acceptButton.onClick.AddListener(() =>
        {
            retryButton.interactable = false;
            acceptButton.interactable = false;
            var order = DesignTableManager.Me.Order; 
            if(order.IsNullOrEmpty() == false && order.OrderInfo?.OrderGiver != null && order.OrderInfo.OrderGiver.HideGiver == false)
            {
                if(DesignTableManager.Me.Order.AdjustReputation(DesignTableManager.Me.DesignInterface.ScoreDesignAgainstOrder(), () =>
                    {
                        OnConfirmDesign();
                        dialog.Close();
                        AudioClipManager.Me.PlayUISound("PlaySound_ProductTestingRoomManufacture");
                        Leave(true);
                    }))
                {
                    return;
                }
            }
            OnConfirmDesign();
            dialog.Close();
            AudioClipManager.Me.PlayUISound("PlaySound_ProductTestingRoomManufacture");
            Leave(true);
        });
        //dialog.onClose += () => StopAndDeactivate();
        dialog.Show();
        m_dialog = dialog;
    }
    
    private static Dictionary<string, int> s_designConfirms = new();
    
    public static int GetDesignConfirms(string _productLine)
    {
        s_designConfirms.TryGetValue(_productLine.ToLower(), out var value);
        return value;
    }
    
    private static void OnConfirmDesign()
    {
        var line = DesignTableManager.Me.DesignInterface.ProductLine != null ? DesignTableManager.Me.DesignInterface.ProductLine.m_prefabName : "building";
        line = line.ToLower();
        s_designConfirms.TryGetValue(line, out var value);
        s_designConfirms[line] = value + 1;
    }

    public void Abort()
    {
        m_dialog.Close();
        Leave(true);
    }

    private bool m_willLeaveDesignMode;
    void Leave(bool _leaveDesign, bool _tryShowReputation = true)
    {
        if (_leaveDesign)
        {
            switch (m_mode)
            {
                case EProductTestMode.Attack:
					break;
                case EProductTestMode.Defend:
                case EProductTestMode.Model:
                    break;
            }
        }

        m_willLeaveDesignMode = _leaveDesign;
        DesignTableManager.Me.SetLeaveAfterTest(_leaveDesign);
        StopAndDeactivate();
    }

    void StopAndDeactivate()
    {
        AnimationOverride.Stop(m_character1Handler.gameObject, true);
        AnimationOverride.Stop(m_character2Handler.gameObject, true);
        Deactivate();
    }

    private int m_deactivateCooldown = 0;
    override public bool DeactivateComplete()
    {
        foreach (var item in m_addedObjects) Destroy(item);
        m_addedObjects.Clear();
        
        if (m_receiverRC != null)
        {
            if (m_deactivateCooldown == 0)
            {
                m_receiverRC.StartAnimatedState();
                var heroAH = m_character2.GetComponentInChildren<AnimationHandler>();
                var workerAH = m_character1.GetComponentInChildren<AnimationHandler>();
                AnimationOverride.PlayClip(heroAH.gameObject, "HeroShout", null);
                AnimationOverride.PlayClip(workerAH.gameObject, "WorkerWave", null);
                m_deactivateCooldown = 4;
                return false;
            }
            --m_deactivateCooldown;
            if (m_deactivateCooldown > 0)
                return false;
        }
        KeyboardShortcutManager.Me.PopShortcuts();
        base.DeactivateComplete();
        RemoveAllHeldObjects();
        GameManager.Me.m_subSceneExitButton?.SetActive(true);
        if (m_willLeaveDesignMode)
            Crossfade.Me.StartFadeOverride(true, false);
        return true;
    }
}
